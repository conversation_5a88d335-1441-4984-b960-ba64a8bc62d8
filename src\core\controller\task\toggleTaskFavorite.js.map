{"version": 3, "file": "toggleTaskFavorite.js", "sourceRoot": "", "sources": ["toggleTaskFavorite.ts"], "names": [], "mappings": "AAIA,MAAM,CAAC,KAAK,UAAU,kBAAkB,CAAC,UAAsB,EAAE,OAA4B;IAC5F,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,OAAO,CAAC,WAAW,KAAK,SAAS,EAAE,CAAC;QAC1D,MAAM,QAAQ,GAAG,qEAAqE,CAAA;QACtF,OAAO,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACvB,OAAO,EAAE,CAAA;IACV,CAAC;IAED,IAAI,CAAC;QACJ,8BAA8B;QAC9B,IAAI,CAAC;YACJ,MAAM,OAAO,GAAI,CAAC,MAAM,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,CAAC,CAAW,IAAI,EAAE,CAAA;YAE1F,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,CAAC,MAAM,CAAC,CAAA;YAEzE,IAAI,SAAS,KAAK,CAAC,CAAC,EAAE,CAAC;gBACtB,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAA;YACrE,CAAC;iBAAM,CAAC;gBACP,0EAA0E;gBAC1E,MAAM,cAAc,GAAG,CAAC,GAAG,OAAO,CAAC,CAAA;gBACnC,cAAc,CAAC,SAAS,CAAC,GAAG;oBAC3B,GAAG,cAAc,CAAC,SAAS,CAAC;oBAC5B,WAAW,EAAE,OAAO,CAAC,WAAW;iBAChC,CAAA;gBAED,kDAAkD;gBAClD,IAAI,CAAC;oBACJ,MAAM,UAAU,CAAC,OAAO,CAAC,WAAW,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAA;gBAC3E,CAAC;gBAAC,OAAO,QAAQ,EAAE,CAAC;oBACnB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,QAAQ,CAAC,CAAA;gBACxD,CAAC;YACF,CAAC;QACF,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,UAAU,CAAC,CAAA;QAC5D,CAAC;QAED,kBAAkB;QAClB,IAAI,CAAC;YACJ,MAAM,UAAU,CAAC,kBAAkB,EAAE,CAAA;QACtC,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAA;QACvD,CAAC;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAA;IACrD,CAAC;IAED,OAAO,EAAE,CAAA;AACV,CAAC"}