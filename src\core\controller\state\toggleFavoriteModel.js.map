{"version": 3, "file": "toggleFavoriteModel.js", "sourceRoot": "", "sources": ["toggleFavoriteModel.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,gBAAgB,EAAE,MAAM,+CAA+C,CAAA;AAEhF,OAAO,EAAE,KAAK,EAAiB,MAAM,8BAA8B,CAAA;AACnE,OAAO,EAAE,iBAAiB,EAAE,MAAM,sBAAsB,CAAA;AAExD;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,mBAAmB,CAAC,UAAsB,EAAE,OAAsB;IACvF,IAAI,CAAC;QACJ,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAA;QACxC,CAAC;QAED,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAA;QAC7B,MAAM,EAAE,gBAAgB,EAAE,GAAG,MAAM,UAAU,CAAC,uBAAuB,EAAE,CAAA;QAEvE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACvB,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;QAC/C,CAAC;QAED,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,iBAAiB,IAAI,EAAE,CAAA;QAElE,yBAAyB;QACzB,MAAM,gBAAgB,GAAG,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC;YAC3D,CAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,KAAK,OAAO,CAAC;YAClD,CAAC,CAAC,CAAC,GAAG,iBAAiB,EAAE,OAAO,CAAC,CAAA;QAElC,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,mBAAmB,EAAE,gBAAgB,CAAC,CAAA;QAElF,8CAA8C;QAC9C,MAAM,WAAW,GAAG,CAAC,iBAAiB,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACxD,gBAAgB,CAAC,0BAA0B,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;QAEjE,iEAAiE;QACjE,MAAM,UAAU,CAAC,kBAAkB,EAAE,CAAA;QAErC,OAAO,KAAK,CAAC,MAAM,EAAE,CAAA;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,8CAA8C,OAAO,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAA;QACpF,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}