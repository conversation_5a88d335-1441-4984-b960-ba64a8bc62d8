// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
import { createServiceRegistry } from "../grpc-service"
import { registerAllMethods } from "./methods"
// Create task service registry
const taskService = createServiceRegistry("task")
export const registerMethod = taskService.registerMethod
// Export the request handlers
export const handleTaskServiceRequest = taskService.handleRequest
export const handleTaskServiceStreamingRequest = taskService.handleStreamingRequest
export const isStreamingMethod = taskService.isStreamingMethod
// Register all task methods
registerAllMethods()
//# sourceMappingURL=index.js.map
