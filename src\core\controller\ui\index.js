// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
import { createServiceRegistry } from "../grpc-service"
import { registerAllMethods } from "./methods"
// Create ui service registry
const uiService = createServiceRegistry("ui")
export const registerMethod = uiService.registerMethod
// Export the request handlers
export const handleUiServiceRequest = uiService.handleRequest
export const handleUiServiceStreamingRequest = uiService.handleStreamingRequest
export const isStreamingMethod = uiService.isStreamingMethod
// Register all ui methods
registerAllMethods()
//# sourceMappingURL=index.js.map
