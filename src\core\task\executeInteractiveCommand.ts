import { Logger } from "@services/logging/Logger"
import { CommandData } from "@integrations/terminal/InteractiveCommand"
import * as path from "path"
import * as vscode from "vscode"
import { DebugLogger } from "@utils/debug-logger"

// 定义ToolResponse类型
type ToolResponse = string

/**
 * Executes an interactive command with expected interactions
 * This method is designed to be called from the Task class
 *
 * @param terminalManager The terminal manager instance
 * @param commandData Command data including interactions
 * @returns A tuple with [userRejected, response]
 */
export async function executeInteractiveCommandTool(
  terminalManager: any,
  commandData: CommandData,
  say: (type: string, text: string) => Promise<void>
): Promise<[boolean, ToolResponse]> {
  const startTime = Date.now()
  const functionId = `exec_cmd_${Date.now().toString(36)}`

  DebugLogger.logLLM(`[EXEC_CMD:${functionId}] ====== 开始执行交互式命令 ======`)
  DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令: ${commandData.command}`)
  DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 开始时间: ${new Date(startTime).toISOString()}`)
  DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 交互步骤数: ${commandData.interactions?.length || 0}`)

  Logger.info(`Executing interactive command: ${commandData.command}`)

  // 检查是否在测试模式
  const isTestMode = process.env.VSCODE_TEST === "1" || process.env.NODE_ENV === "test"
  if (isTestMode) {
    // 在测试模式下，只记录命令并返回
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 检测到测试模式，不实际执行命令`)
    Logger.info(`[TEST MODE] Interactive command: ${JSON.stringify(commandData, null, 2)}`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 返回模拟响应`)
    return [false, `[TEST MODE] Interactive command would execute: ${commandData.command} with ${commandData.interactions?.length || 0} interactions`]
  }

  try {
    // 获取当前工作目录
    const workingDir = vscode.workspace.workspaceFolders?.[0]?.uri.fsPath || process.cwd()
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 工作目录: ${workingDir}`)

    // 获取或创建终端
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 正在获取或创建终端...`)
    const terminalInfo = await terminalManager.getOrCreateTerminal(workingDir)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 终端创建成功，ID: ${terminalInfo.id}`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 终端类型: ${terminalInfo.terminalType || 'unknown'}`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] Shell类型: ${terminalInfo.shellType || 'unknown'}`)

    terminalInfo.terminal.show()
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 终端已显示`)

    // Log the interactions
    if (commandData.interactions && commandData.interactions.length > 0) {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令有 ${commandData.interactions.length} 个交互步骤:`)
      commandData.interactions.forEach((interaction, index) => {
        DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 步骤 ${index + 1}:
          - 提示: "${interaction.prompt}"
          - 响应: ${interaction.hidden ? "[隐藏]" : `"${interaction.response}"`}
          - 可选: ${interaction.optional || false}`)
        Logger.info(`  ${index + 1}. Prompt: "${interaction.prompt}", Response: ${interaction.hidden ? "[HIDDEN]" : `"${interaction.response}"`}`)
      })
    } else {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令没有交互步骤，将直接执行`)
    }

    // Run the interactive command
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 开始执行命令: ${commandData.command}`)
    const commandStartTime = Date.now()
    const commandProcess = terminalManager.runInteractiveCommand(terminalInfo, commandData)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令进程已启动，等待输出...`)

    // Collect output
    let result = ""
    let lineCount = 0
    commandProcess.on("line", (line: string) => {
      lineCount++
      result += line + "\n"

      // 记录每行输出（但限制日志量）
      if (lineCount <= 20 || lineCount % 50 === 0) {
        DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 输出行 #${lineCount}: ${line.substring(0, 100)}${line.length > 100 ? '...' : ''}`)
      }

      // Send real-time output to the UI
      say("command_output", line)
    })

    // 记录进程事件
    commandProcess.on("error", (error: any) => {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令进程错误: ${error}`)
    })

    // Wait for completion
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 等待命令完成...`)
    await commandProcess
    const commandEndTime = Date.now()
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令执行完成，耗时: ${commandEndTime - commandStartTime}ms`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 总输出行数: ${lineCount}`)

    // 检查终端状态
    const terminalStatus = terminalManager.interactionHandler.getInteractionStatus(terminalInfo.id)
    if (terminalStatus) {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 交互状态: 当前步骤=${terminalStatus.current}/${terminalStatus.total}`)
    } else {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 交互状态: 无活动交互或已完成`)
    }

    // 检查终端是否仍然忙碌
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 终端忙碌状态: ${terminalInfo.busy ? '忙碌' : '空闲'}`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 终端会话类型: ${terminalInfo.sessionType || 'unknown'}`)

    // Check if SSH connection was successful for SSH commands
    if (commandData.command.trim().startsWith('ssh ') && terminalInfo.terminalType === 'remote') {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 检测到SSH连接成功，远程地址: ${terminalInfo.remoteAddress}`)
      result += `\n[SSH Connection Established to ${terminalInfo.remoteAddress}]`
    }

    // 记录结果摘要
    const resultPreview = result.substring(0, 200) + (result.length > 200 ? '...' : '')
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 命令执行结果 (前200字符): ${resultPreview}`)

    // 检查是否有任何未完成的交互
    const finalTerminalStatus = terminalManager.interactionHandler.getInteractionStatus(terminalInfo.id)
    if (finalTerminalStatus) {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 警告: 交互似乎未完全完成，当前步骤=${finalTerminalStatus.current}/${finalTerminalStatus.total}`)
    }

    // 检查userMessageContent状态
    try {
      const taskInstance = (say as any).task || {}
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 任务状态检查:`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - userMessageContentReady: ${taskInstance.userMessageContentReady}`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - userMessageContent长度: ${taskInstance.userMessageContent?.length || 0}`)
    } catch (e) {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 无法检查任务状态: ${e}`)
    }

    const endTime = Date.now()
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] ====== 交互式命令执行完成 ======`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 总耗时: ${endTime - startTime}ms`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 返回结果到LLM: ${result.length} 字符`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 结果预览: ${result.substring(0, 500)}${result.length > 500 ? '...' : ''}`)

    // 检查任务状态
    try {
      const taskInstance = (say as any).task || {}
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 返回结果前的任务状态:`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - userMessageContentReady: ${taskInstance.userMessageContentReady}`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - didCompleteReadingStream: ${taskInstance.didCompleteReadingStream}`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - currentStreamingContentIndex: ${taskInstance.currentStreamingContentIndex}`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - assistantMessageContent长度: ${taskInstance.assistantMessageContent?.length || 0}`)
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] - userMessageContent长度: ${taskInstance.userMessageContent?.length || 0}`)
    } catch (e) {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 无法检查任务状态: ${e}`)
    }

    const finalResult = `Interactive command executed.\n${result}`
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 最终返回结果长度: ${finalResult.length} 字符`)

    return [false, finalResult]
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error)
    const stackTrace = error instanceof Error ? error.stack : 'No stack trace available'

    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] ====== 交互式命令执行出错 ======`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 错误信息: ${errorMessage}`)
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 错误堆栈: ${stackTrace}`)

    // 记录错误发生时的上下文
    try {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 错误上下文:
        - 命令: ${commandData.command}
        - 交互步骤数: ${commandData.interactions?.length || 0}
        - 执行时间: ${Date.now() - startTime}ms`)
    } catch (e) {
      DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 无法记录错误上下文: ${e}`)
    }

    Logger.error(`Error executing interactive command: ${errorMessage}`)

    const endTime = Date.now()
    DebugLogger.logLLM(`[EXEC_CMD:${functionId}] 总耗时 (错误): ${endTime - startTime}ms`)

    return [false, `Error executing interactive command: ${errorMessage}`]
  }
}
