{"version": 3, "file": "extension.test.js", "sourceRoot": "", "sources": ["extension.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,MAAM,aAAa,CAAA;AACtC,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,EAAE,MAAM,OAAO,CAAA;AAC3C,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,QAAQ,CAAA;AACf,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAEhC,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,cAAc,CAAC,CAAA;AAEpE,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAChC,KAAK,CAAC,GAAG,EAAE;QACV,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iBAAiB,CAAC,CAAA;IACxD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,iDAAiD,EAAE,KAAK,IAAI,EAAE;QAChE,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,QAAQ,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC,CAAA;QACnE,MAAM,EAAE,GAAG,WAAW,CAAC,SAAS,GAAG,GAAG,GAAG,WAAW,CAAC,IAAI,CAAA;QACzD,MAAM,iBAAiB,GAAG,MAAM,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC,CAAA;QAE5D,iBAAiB,EAAE,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;IACvC,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAA;QACxD,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,yBAAyB,CAAC,CAAA;IAChE,CAAC,CAAC,CAAA;IAEF,oDAAoD;IACpD,EAAE,CAAC,2CAA2C,EAAE,KAAK,IAAI,EAAE;QAC1D,yBAAyB;QACzB,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,YAAY,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;YAClG,aAAa,EAAE,IAAI;SACnB,CAAC,CAAA;QAEF,wBAAwB;QACxB,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG;;;;;;;;;;;GAWpB,CAAA;QAED,sBAAsB;QACtB,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;QACnB,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QAE9B,WAAW;QACX,KAAK,CAAC,OAAO,EAAE,CAAA;IAChB,CAAC,CAAC,CAAA;IAEF,+BAA+B;IAC/B,EAAE,CAAC,gCAAgC,EAAE,KAAK,IAAI,EAAE;QAC/C,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,aAAa,EAAE,cAAc,EAAE,MAAM,CAAC,UAAU,CAAC,GAAG,EAAE;YACpG,aAAa,EAAE,IAAI;SACnB,CAAC,CAAA;QAEF,0BAA0B;QAC1B,MAAM,cAAc,GAAG,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,EAAE;YACtD,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,SAAS,CAAC,CAAA;QACjF,CAAC,CAAC,CAAA;QAEF,6BAA6B;QAC7B,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG;;;;;;;;;;;;;;GAcpB,CAAA;QAED,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,cAAc,CAAA;QACpC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QAEpC,WAAW;QACX,KAAK,CAAC,OAAO,EAAE,CAAA;IAChB,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}