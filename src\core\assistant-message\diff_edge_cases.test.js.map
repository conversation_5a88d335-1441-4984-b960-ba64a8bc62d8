{"version": 3, "file": "diff_edge_cases.test.js", "sourceRoot": "", "sources": ["diff_edge_cases.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,uBAAuB,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAA;AACzD,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,OAAO,CAAA;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAA;AAE7B,KAAK,UAAU,IAAI,CAAC,WAAmB,EAAE,eAAuB,EAAE,OAAgB;IACjF,OAAO,KAAK,CAAC,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;AAC1D,CAAC;AAED,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACvC,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QAClE,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,wBAAwB,CAAA;QACzC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QACzC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QAClE,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,wBAAwB,CAAA;QACzC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QACzC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,wBAAwB,CAAA;QACzC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,wBAAwB,CAAA;QACzC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,wBAAwB,CAAA;QACzC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,8DAA8D,EAAE,KAAK,IAAI,EAAE;QAC7E,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,wBAAwB,CAAA;QACzC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7B,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,iFAAiF,EAAE,KAAK,IAAI,EAAE;QAChG,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,mDAAmD,CAAA;QACpE,MAAM,IAAI,GAAG;;;;;;;;;gBASC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iDAAiD,CAAC,CAAA;QAC3E,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAA;IACtF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,2GAA2G,EAAE,KAAK,IAAI,EAAE;QAC1H,MAAM,OAAO,GAAG,IAAI,CAAA;QACpB,MAAM,QAAQ,GAAG,mDAAmD,CAAA;QACpE,MAAM,IAAI,GAAG;;;;;;;;;gBASC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;QACpD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;QACxD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,2DAA2D,CAAC,CAAA;IACtF,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}