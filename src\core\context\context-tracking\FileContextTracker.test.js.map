{"version": 3, "file": "FileContextTracker.test.js", "sourceRoot": "", "sources": ["FileContextTracker.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,OAAO,CAAA;AAC3D,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAA;AAC7B,OAAO,KAAK,KAAK,MAAM,OAAO,CAAA;AAC9B,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAChC,OAAO,KAAK,IAAI,MAAM,MAAM,CAAA;AAC5B,OAAO,EAAE,kBAAkB,EAAE,MAAM,sBAAsB,CAAA;AACzD,OAAO,KAAK,UAAU,MAAM,oBAAoB,CAAA;AAGhD,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IACnC,IAAI,OAA2B,CAAA;IAC/B,IAAI,WAAoC,CAAA;IACxC,IAAI,aAA8B,CAAA;IAClC,IAAI,qBAA0B,CAAA;IAC9B,IAAI,OAA2B,CAAA;IAC/B,IAAI,MAAc,CAAA;IAClB,IAAI,gBAA8B,CAAA;IAClC,IAAI,mBAAoC,CAAA;IACxC,IAAI,oBAAqC,CAAA;IAEzC,UAAU,CAAC,GAAG,EAAE;QACf,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAA;QAE/B,wBAAwB;QACxB,aAAa,GAAG,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE,kBAAkB,CAAC,CAAC,KAAK,CAAC;YACxE;gBACC,GAAG,EAAE;oBACJ,MAAM,EAAE,iBAAiB;iBACzB;aACyB;SAC3B,CAAC,CAAA;QAEF,2BAA2B;QAC3B,qBAAqB,GAAG;YACvB,OAAO,EAAE,OAAO,CAAC,IAAI,EAAE;YACvB,WAAW,EAAE,OAAO,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC,EAAE,CAAC;SAC1D,CAAA;QAED,sDAAsD;QACtD,MAAM,+BAA+B,GAAG,MAAM,CAAC,SAAS,CAAC,uBAAuB,CAAA;QAChF,MAAM,CAAC,SAAS,CAAC,uBAAuB,GAAG;YAC1C,OAAO,qBAAqB,CAAA;QAC7B,CAAC,CAAA;QAED,8BAA8B;QAC9B,WAAW,GAAG;YACb,gBAAgB,EAAE,EAAE,MAAM,EAAE,eAAe,EAAE;SACP,CAAA;QAEvC,6BAA6B;QAC7B,gBAAgB,GAAG,EAAE,gBAAgB,EAAE,EAAE,EAAE,WAAW,EAAE,EAAE,EAAE,CAAA;QAC5D,mBAAmB,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,iBAAiB,CAAC,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;QAC5F,oBAAoB,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC,QAAQ,EAAE,CAAA;QAE9E,0BAA0B;QAC1B,MAAM,GAAG,cAAc,CAAA;QACvB,OAAO,GAAG,IAAI,kBAAkB,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;IACtD,CAAC,CAAC,CAAA;IAEF,SAAS,CAAC,GAAG,EAAE;QACd,OAAO,CAAC,OAAO,EAAE,CAAA;IAClB,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,mDAAmD,EAAE,KAAK,IAAI,EAAE;QAClE,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAErD,oCAAoC;QACpC,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;QACjD,MAAM,CAAC,mBAAmB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA;QAE9D,2DAA2D;QAC3D,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;QAElD,MAAM,aAAa,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAEzD,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;QACnD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACzC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACjD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,WAAW,CAAC,CAAA;QACrD,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QACnD,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;IAC7C,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,oDAAoD,EAAE,KAAK,IAAI,EAAE;QACnE,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QAExD,2DAA2D;QAC3D,MAAM,CAAC,oBAAoB,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;QAClD,MAAM,aAAa,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAE5D,4DAA4D;QAC5D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,KAAK,CAAA;QAE1E,sCAAsC;QACtC,MAAM,WAAW,GAAG,aAAa,CAAC,gBAAgB,CAAC,IAAI,CACtD,CAAC,KAAwB,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,QAAQ,IAAI,KAAK,CAAC,YAAY,KAAK,QAAQ,CACxF,CAAA;QAED,uCAAuC;QACvC,MAAM,CAAC,WAAW,CAAC,CAAC,EAAE,CAAC,KAAK,CAAA;QAE5B,+CAA+C;QAC/C,MAAM,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAC3C,MAAM,CAAC,WAAW,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACnD,MAAM,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QAC1D,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QACrD,MAAM,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;IACtD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,8CAA8C,EAAE,KAAK,IAAI,EAAE;QAC7D,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,gBAAgB,CAAC,CAAA;QAE1D,2DAA2D;QAC3D,MAAM,aAAa,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;QAEnD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACzC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACjD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAA;QAC1D,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QACnD,MAAM,CAAC,SAAS,CAAC,eAAe,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;IAC7C,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;QACtE,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAA;QAEvD,2DAA2D;QAC3D,MAAM,aAAa,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,MAAM,SAAS,GAAG,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;QAEnD,MAAM,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACzC,MAAM,CAAC,SAAS,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACjD,MAAM,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;QACvD,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;QAElD,qDAAqD;QACrD,MAAM,aAAa,GAAG,OAAO,CAAC,gCAAgC,EAAE,CAAA;QAChE,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC3C,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,iFAAiF,EAAE,KAAK,IAAI,EAAE;QAChG,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,uBAAuB;QACvB,gBAAgB,CAAC,gBAAgB,GAAG;YACnC;gBACC,IAAI,EAAE,QAAQ;gBACd,YAAY,EAAE,QAAQ;gBACtB,aAAa,EAAE,WAAW;gBAC1B,eAAe,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,EAAE,eAAe;gBACnD,eAAe,EAAE,IAAI;gBACrB,cAAc,EAAE,IAAI;aACpB;SACD,CAAA;QAED,yCAAyC;QACzC,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAA;QAExD,qEAAqE;QACrE,MAAM,aAAa,GAAG,oBAAoB,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAC5D,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAA;QAEzD,wCAAwC;QACxC,MAAM,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAExE,6BAA6B;QAC7B,MAAM,QAAQ,GAAG,aAAa,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAA;QAClD,MAAM,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QAChD,MAAM,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;IACxD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;QAC9D,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,8DAA8D;QAC9D,MAAM,gBAAgB,GAAG,KAAK,CAAC,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,yBAAyB,CAAC,CAAA;QAE/E,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAErD,4CAA4C;QAC5C,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;QAC1C,gBAAgB,CAAC,OAAO,EAAE,CAAA;QAE1B,8DAA8D;QAC9D,MAAM,CAAC,qBAAqB,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;IAC5D,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;QAC1E,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,6CAA6C;QAC7C,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAErD,0CAA0C;QAC1C,mBAAmB,CAAC,YAAY,EAAE,CAAA;QAClC,oBAAoB,CAAC,YAAY,EAAE,CAAA;QAEnC,mFAAmF;QACnF,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;QAEpE,wDAAwD;QACxD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEpE,6DAA6D;QAC7D,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;QAEpE,+DAA+D;QAC/D,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;QAE1E,qDAAqD;QACrD,MAAM,aAAa,GAAG,OAAO,CAAC,gCAAgC,EAAE,CAAA;QAChE,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC3C,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,6CAA6C;QAC7C,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAErD,mCAAmC;QACnC,OAAO,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAA;QAEzC,0CAA0C;QAC1C,mBAAmB,CAAC,YAAY,EAAE,CAAA;QAClC,oBAAoB,CAAC,YAAY,EAAE,CAAA;QAEnC,6DAA6D;QAC7D,MAAM,mBAAmB,GAAG,OAAO,CAAC,GAAG,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;QAEpE,wDAAwD;QACxD,MAAM,QAAQ,GAAG,qBAAqB,CAAC,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAEpE,6DAA6D;QAC7D,QAAQ,CAAC,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,iBAAiB,EAAE,QAAQ,CAAC,CAAC,CAAC,CAAA;QAEpE,0DAA0D;QAC1D,MAAM,CAAC,mBAAmB,CAAC,UAAU,CAAC,QAAQ,EAAE,aAAa,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,CAAA;QAE3E,yDAAyD;QACzD,MAAM,aAAa,GAAG,OAAO,CAAC,gCAAgC,EAAE,CAAA;QAChE,MAAM,CAAC,aAAa,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;IAC/C,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;QACpE,MAAM,QAAQ,GAAG,kBAAkB,CAAA;QAEnC,qCAAqC;QACrC,MAAM,OAAO,CAAC,gBAAgB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAA;QAErD,eAAe;QACf,OAAO,CAAC,OAAO,EAAE,CAAA;QAEjB,kCAAkC;QAClC,MAAM,CAAC,qBAAqB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAA;IACxD,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}