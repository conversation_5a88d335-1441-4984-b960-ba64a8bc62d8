// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
import { createServiceRegistry } from "../grpc-service"
import { registerAllMethods } from "./methods"
// Create mcp service registry
const mcpService = createServiceRegistry("mcp")
export const registerMethod = mcpService.registerMethod
// Export the request handlers
export const handleMcpServiceRequest = mcpService.handleRequest
export const handleMcpServiceStreamingRequest = mcpService.handleStreamingRequest
export const isStreamingMethod = mcpService.isStreamingMethod
// Register all mcp methods
registerAllMethods()
//# sourceMappingURL=index.js.map
