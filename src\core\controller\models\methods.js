// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
// Import all method implementations
import { registerMethod } from "./index"
import { getLmStudioModels } from "./getLmStudioModels"
import { getOllamaModels } from "./getOllamaModels"
import { getVsCodeLmModels } from "./getVsCodeLmModels"
import { refreshOpenAiModels } from "./refreshOpenAiModels"
import { refreshOpenRouterModels } from "./refreshOpenRouterModels"
import { refreshRequestyModels } from "./refreshRequestyModels"
// Register all models service methods
export function registerAllMethods() {
	// Register each method with the registry
	registerMethod("getLmStudioModels", getLmStudioModels)
	registerMethod("getOllamaModels", getOllamaModels)
	registerMethod("getVsCodeLmModels", getVsCodeLmModels)
	registerMethod("refreshOpenAiModels", refreshOpenAiModels)
	registerMethod("refreshOpenRouterModels", refreshOpenRouterModels)
	registerMethod("refreshRequestyModels", refreshRequestyModels)
}
//# sourceMappingURL=methods.js.map
