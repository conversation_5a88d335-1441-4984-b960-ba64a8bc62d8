{"version": 3, "file": "deleteNonFavoritedTasks.js", "sourceRoot": "", "sources": ["deleteNonFavoritedTasks.ts"], "names": [], "mappings": "AAAA,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,MAAM,aAAa,CAAA;AAI5B,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,MAAM,qBAAqB,CAAA;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,mBAAmB,CAAA;AAEpD;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC5C,UAAsB,EACtB,QAAsB;IAEtB,IAAI,CAAC;QACJ,2BAA2B;QAC3B,MAAM,UAAU,CAAC,SAAS,EAAE,CAAA;QAE5B,4BAA4B;QAC5B,MAAM,WAAW,GAAI,CAAC,MAAM,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAW,IAAI,EAAE,CAAA;QAE9F,iCAAiC;QACjC,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,WAAW,KAAK,IAAI,CAAC,CAAA;QAC9E,MAAM,YAAY,GAAG,WAAW,CAAC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAA;QAE/D,OAAO,CAAC,GAAG,CAAC,mCAAmC,cAAc,CAAC,MAAM,8BAA8B,CAAC,CAAA;QAEnG,sBAAsB;QACtB,IAAI,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC/B,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,cAAc,CAAC,CAAA;QAC3E,CAAC;aAAM,CAAC;YACP,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,EAAE,SAAS,CAAC,CAAA;QACtE,CAAC;QAED,+CAA+C;QAC/C,MAAM,eAAe,GAAG,cAAc,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QAC7D,MAAM,gBAAgB,CAAC,UAAU,EAAE,eAAe,CAAC,CAAA;QAEnD,iBAAiB;QACjB,IAAI,CAAC;YACJ,MAAM,UAAU,CAAC,kBAAkB,EAAE,CAAA;QACtC,CAAC;QAAC,OAAO,UAAU,EAAE,CAAC;YACrB,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,UAAU,CAAC,CAAA;QACvD,CAAC;QAED,OAAO;YACN,cAAc,EAAE,cAAc,CAAC,MAAM;YACrC,YAAY,EAAE,YAAY;SAC1B,CAAA;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;QACzD,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gBAAgB,CAAC,UAAsB,EAAE,eAAyB;IAChF,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAElF,IAAI,CAAC;QACJ,IAAI,MAAM,gBAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;YACzC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAChC,MAAM,QAAQ,GAAG,MAAM,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC,CAAA;gBAC9C,OAAO,CAAC,KAAK,CAAC,4BAA4B,QAAQ,CAAC,MAAM,mBAAmB,CAAC,CAAA;gBAE7E,6CAA6C;gBAC7C,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,CAAC;oBAC5B,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;wBACpC,MAAM,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;oBAC3E,CAAC;gBACF,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,0CAA0C;gBAC1C,MAAM,EAAE,CAAC,EAAE,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;YAC3D,CAAC;QACF,CAAC;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAA;IACtD,CAAC;IAED,OAAO,IAAI,CAAA;AACZ,CAAC"}