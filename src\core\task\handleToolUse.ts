import { Logger } from "@services/logging/Logger"
import { CommandData } from "@integrations/terminal/InteractiveCommand"
import { Task } from "."
import { DebugLogger } from "@utils/debug-logger"

// 定义ToolUse类型
type ToolResponse = string;

interface ToolUse {
  name: string;
  params: Record<string, any>;
}

/**
 * Handles tool use requests from the LLM
 * @param task The task instance
 * @param toolUse The tool use request
 * @returns The tool response
 */
export async function handleToolUse(task: Task, toolUse: ToolUse): Promise<any> {
  const toolName = toolUse.name
  const toolParams = toolUse.params

  Logger.info(`Handling tool use: ${toolName}`)
  DebugLogger.logLLM(`Handling tool use: ${toolName}`)
  DebugLogger.logLLM(`Tool parameters: ${JSON.stringify(toolParams, null, 2)}`)

  switch (toolName) {
    case "execute_command": {
      const command = toolParams.command
      const requiresApproval = toolParams.requires_approval === "true"
      const [userRejected, response] = await task.executeCommandTool(command)
      if (userRejected) {
        return "User rejected command execution."
      }
      return response
    }

    case "execute_interactive_command": {
      try {
        // 记录工具调用的开始时间
        const startTime = Date.now()
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 开始处理交互式命令工具调用 (时间: ${new Date(startTime).toISOString()})`)

        // Parse the command data
        const commandDataStr = toolParams.command_data
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 原始命令数据: ${commandDataStr}`)

        const commandData = JSON.parse(commandDataStr) as CommandData

        Logger.info(`Executing interactive command: ${commandData.command}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 执行交互式命令: ${commandData.command}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 命令有 ${commandData.interactions?.length || 0} 个交互步骤`)

        if (commandData.interactions) {
          commandData.interactions.forEach((interaction, index) => {
            DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 交互步骤 ${index + 1}:
              - 提示: "${interaction.prompt}"
              - 响应: ${interaction.hidden ? "[隐藏]" : `"${interaction.response}"`}
              - 可选: ${interaction.optional || false}`)
          })
        }

        // 记录调用前的状态
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 准备调用 executeInteractiveCommandTool 方法...`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 任务ID: ${(task as any).taskId || 'unknown'}`)

        // Execute the interactive command
        const executeStartTime = Date.now()
        const toolCallId = `tool_${Date.now().toString(36)}`
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 开始执行命令 (时间: ${new Date(executeStartTime).toISOString()})`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 命令: ${commandData.command}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 交互步骤数: ${commandData.interactions?.length || 0}`)

        // 记录任务状态
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 执行前任务状态:`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - userMessageContentReady: ${(task as any).userMessageContentReady}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - didCompleteReadingStream: ${(task as any).didCompleteReadingStream}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - currentStreamingContentIndex: ${(task as any).currentStreamingContentIndex}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - assistantMessageContent长度: ${(task as any).assistantMessageContent?.length || 0}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - userMessageContent长度: ${(task as any).userMessageContent?.length || 0}`)



        const [userRejected, response] = await task.executeInteractiveCommandTool(commandData)
        const executeEndTime = Date.now()
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 命令执行完成 (耗时: ${executeEndTime - executeStartTime}ms)`)

        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 调用executeInteractiveCommandTool后的任务状态:`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - userMessageContentReady: ${(task as any).userMessageContentReady}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - didCompleteReadingStream: ${(task as any).didCompleteReadingStream}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - currentStreamingContentIndex: ${(task as any).currentStreamingContentIndex}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - assistantMessageContent长度: ${(task as any).assistantMessageContent?.length || 0}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - userMessageContent长度: ${(task as any).userMessageContent?.length || 0}`)

        // 记录执行后的任务状态
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] 执行后任务状态:`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - userMessageContentReady: ${(task as any).userMessageContentReady}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - didCompleteReadingStream: ${(task as any).didCompleteReadingStream}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - currentStreamingContentIndex: ${(task as any).currentStreamingContentIndex}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - assistantMessageContent长度: ${(task as any).assistantMessageContent?.length || 0}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND:${toolCallId}] - userMessageContent长度: ${(task as any).userMessageContent?.length || 0}`)

        if (userRejected) {
          DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 用户拒绝了交互式命令执行`)
          return "User rejected interactive command execution."
        }

        // 记录响应详情
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 交互式命令执行成功完成`)
        if (typeof response === 'string') {
          DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 响应类型: string, 长度: ${response.length} 字符`)
          DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 响应样本: ${response.substring(0, 200)}${response.length > 200 ? '...' : ''}`)
        } else {
          DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 响应类型: ${typeof response}, 值: ${JSON.stringify(response).substring(0, 200)}...`)
        }

        // 记录总耗时
        const endTime = Date.now()
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 交互式命令处理完成 (总耗时: ${endTime - startTime}ms)`)

        return response
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error)
        const stackTrace = error instanceof Error ? error.stack : 'No stack trace available'

        Logger.error(`Error executing interactive command: ${errorMessage}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 执行交互式命令时出错: ${errorMessage}`)
        DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 错误堆栈: ${stackTrace}`)

        // 尝试记录更多上下文信息
        try {
          DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 错误上下文:
            - 命令: ${toolParams.command_data ? JSON.parse(toolParams.command_data).command : 'unknown'}
            - 任务ID: ${(task as any).taskId || 'unknown'}`)
        } catch (e) {
          DebugLogger.logLLM(`[INTERACTIVE_COMMAND] 无法记录错误上下文: ${e}`)
        }

        return `Error executing interactive command: ${errorMessage}`
      }
    }

    // Add other tool cases here

    default:
      Logger.error(`Unknown tool: ${toolName}`)
      return `Unknown tool: ${toolName}`
  }
}
