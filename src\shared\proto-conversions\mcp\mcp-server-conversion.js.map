{"version": 3, "file": "mcp-server-conversion.js", "sourceRoot": "", "sources": ["mcp-server-conversion.ts"], "names": [], "mappings": "AACA,OAAO,EAKN,eAAe,GACf,MAAM,iBAAiB,CAAA;AAExB,4CAA4C;AAC5C,SAAS,uBAAuB,CAAC,MAA2B;IAC3D,QAAQ,MAAM,EAAE,CAAC;QAChB,KAAK,WAAW;YACf,OAAO,eAAe,CAAC,2BAA2B,CAAA;QACnD,KAAK,YAAY;YAChB,OAAO,eAAe,CAAC,4BAA4B,CAAA;QACpD,KAAK,cAAc;YAClB,OAAO,eAAe,CAAC,8BAA8B,CAAA;IACvD,CAAC;AACF,CAAC;AAED,MAAM,UAAU,kCAAkC,CAAC,UAAuB;IACzE,MAAM,YAAY,GAAqB,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;QAClE,IAAI,EAAE,MAAM,CAAC,IAAI;QACjB,MAAM,EAAE,MAAM,CAAC,MAAM;QACrB,MAAM,EAAE,uBAAuB,CAAC,MAAM,CAAC,MAAM,CAAC;QAC9C,KAAK,EAAE,MAAM,CAAC,KAAK;QAEnB,uBAAuB;QACvB,KAAK,EAAE,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,WAAW,CAAC;QAC5C,SAAS,EAAE,CAAC,MAAM,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,eAAe,CAAC;QACxD,iBAAiB,EAAE,CAAC,MAAM,CAAC,iBAAiB,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,uBAAuB,CAAC;QAEhF,QAAQ,EAAE,MAAM,CAAC,QAAQ;QACzB,OAAO,EAAE,MAAM,CAAC,OAAO;KACvB,CAAC,CAAC,CAAA;IACH,OAAO,YAAY,CAAA;AACpB,CAAC;AAED;;GAEG;AACH,SAAS,WAAW,CAAC,IAAa;IACjC,MAAM,iBAAiB,GAAG,IAAI,CAAC,WAAW;QACzC,CAAC,CAAC,OAAO,IAAI,CAAC,WAAW,KAAK,QAAQ;YACrC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC;YAClC,CAAC,CAAC,IAAI,CAAC,WAAW;QACnB,CAAC,CAAC,SAAS,CAAA;IAEZ,OAAO;QACN,IAAI,EAAE,IAAI,CAAC,IAAI;QACf,WAAW,EAAE,IAAI,CAAC,WAAW;QAC7B,WAAW,EAAE,iBAAiB;QAC9B,WAAW,EAAE,IAAI,CAAC,WAAW;KAC7B,CAAA;AACF,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,QAAqB;IAC7C,OAAO;QACN,GAAG,EAAE,QAAQ,CAAC,GAAG;QACjB,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;KACjC,CAAA;AACF,CAAC;AAED;;GAEG;AACH,SAAS,uBAAuB,CAAC,QAA6B;IAC7D,OAAO;QACN,WAAW,EAAE,QAAQ,CAAC,WAAW;QACjC,IAAI,EAAE,QAAQ,CAAC,IAAI;QACnB,QAAQ,EAAE,QAAQ,CAAC,QAAQ;QAC3B,WAAW,EAAE,QAAQ,CAAC,WAAW;KACjC,CAAA;AACF,CAAC;AAED,4CAA4C;AAC5C,SAAS,uBAAuB,CAAC,MAAuB;IACvD,QAAQ,MAAM,EAAE,CAAC;QAChB,KAAK,eAAe,CAAC,2BAA2B;YAC/C,OAAO,WAAW,CAAA;QACnB,KAAK,eAAe,CAAC,4BAA4B;YAChD,OAAO,YAAY,CAAA;QACpB,KAAK,eAAe,CAAC,8BAA8B,CAAC;QACpD,SAAS,gEAAgE;YACxE,OAAO,cAAc,CAAA;IACvB,CAAC;AACF,CAAC;AAED,MAAM,UAAU,kCAAkC,CAAC,YAA8B;IAChF,MAAM,UAAU,GAAgB,YAAY,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,EAAE;QAChE,OAAO;YACN,IAAI,EAAE,WAAW,CAAC,IAAI;YACtB,MAAM,EAAE,WAAW,CAAC,MAAM;YAC1B,MAAM,EAAE,uBAAuB,CAAC,WAAW,CAAC,MAAM,CAAC;YACnD,KAAK,EAAE,WAAW,CAAC,KAAK,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,CAAC,KAAK;YAE/D,uBAAuB;YACvB,KAAK,EAAE,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,gBAAgB,CAAC;YAC9C,SAAS,EAAE,WAAW,CAAC,SAAS,CAAC,GAAG,CAAC,oBAAoB,CAAC;YAC1D,iBAAiB,EAAE,WAAW,CAAC,iBAAiB,CAAC,GAAG,CAAC,4BAA4B,CAAC;YAElF,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,OAAO,EAAE,WAAW,CAAC,OAAO;SAC5B,CAAA;IACF,CAAC,CAAC,CAAA;IACF,OAAO,UAAU,CAAA;AAClB,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,SAAuB;IAChD,OAAO;QACN,IAAI,EAAE,SAAS,CAAC,IAAI;QACpB,WAAW,EAAE,SAAS,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC,WAAW;QAC7E,WAAW,EAAE,SAAS,CAAC,WAAW;YACjC,CAAC,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,CAAC,GAAG,CAAC;gBACtC,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,WAAW,CAAC;gBACnC,CAAC,CAAC,SAAS,CAAC,WAAW;YACxB,CAAC,CAAC,SAAS;QACZ,WAAW,EAAE,SAAS,CAAC,WAAW;KAClC,CAAA;AACF,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,aAA+B;IAC5D,OAAO;QACN,GAAG,EAAE,aAAa,CAAC,GAAG;QACtB,IAAI,EAAE,aAAa,CAAC,IAAI;QACxB,QAAQ,EAAE,aAAa,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ;QAC5E,WAAW,EAAE,aAAa,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW;KACrF,CAAA;AACF,CAAC;AAED;;GAEG;AACH,SAAS,4BAA4B,CAAC,aAAuC;IAC5E,OAAO;QACN,WAAW,EAAE,aAAa,CAAC,WAAW;QACtC,IAAI,EAAE,aAAa,CAAC,IAAI;QACxB,QAAQ,EAAE,aAAa,CAAC,QAAQ,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,QAAQ;QAC5E,WAAW,EAAE,aAAa,CAAC,WAAW,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,aAAa,CAAC,WAAW;KACrF,CAAA;AACF,CAAC"}