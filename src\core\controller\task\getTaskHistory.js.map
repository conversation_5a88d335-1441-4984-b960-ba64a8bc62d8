{"version": 3, "file": "getTaskHistory.js", "sourceRoot": "", "sources": ["getTaskHistory.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,MAAM,qBAAqB,CAAA;AACpD,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAA;AAErE;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAAC,UAAsB,EAAE,OAA8B;IAC1F,IAAI,CAAC;QACJ,MAAM,EAAE,aAAa,EAAE,oBAAoB,EAAE,WAAW,EAAE,MAAM,EAAE,GAAG,OAAO,CAAA;QAE5E,qCAAqC;QACrC,MAAM,WAAW,GAAI,CAAC,MAAM,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,aAAa,CAAC,CAAW,IAAI,EAAE,CAAA;QAC9F,MAAM,aAAa,GAAG,gBAAgB,EAAE,CAAA;QAExC,gBAAgB;QAChB,IAAI,aAAa,GAAG,WAAW,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE;YAC/C,qDAAqD;YACrD,MAAM,iBAAiB,GAAG,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAA;YAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;gBACxB,OAAO,KAAK,CAAA;YACb,CAAC;YAED,sCAAsC;YACtC,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACxC,OAAO,KAAK,CAAA;YACb,CAAC;YAED,8CAA8C;YAC9C,IAAI,oBAAoB,EAAE,CAAC;gBAC1B,IAAI,aAAa,GAAG,KAAK,CAAA;gBAEzB,oGAAoG;gBACpG,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBAClC,IAAI,aAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,EAAE,CAAC;wBAChE,aAAa,GAAG,IAAI,CAAA;oBACrB,CAAC;gBACF,CAAC;gBAED,8FAA8F;gBAC9F,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,uBAAuB,EAAE,CAAC;oBACpD,IAAI,aAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE,aAAa,CAAC,EAAE,CAAC;wBAChE,aAAa,GAAG,IAAI,CAAA;oBACrB,CAAC;gBACF,CAAC;gBAED,IAAI,CAAC,aAAa,EAAE,CAAC;oBACpB,OAAO,KAAK,CAAA;gBACb,CAAC;YACF,CAAC;YAED,OAAO,IAAI,CAAA;QACZ,CAAC,CAAC,CAAA;QAEF,2BAA2B;QAC3B,IAAI,WAAW,EAAE,CAAC;YACjB,+BAA+B;YAC/B,MAAM,KAAK,GAAG,WAAW,CAAC,WAAW,EAAE,CAAA;YACvC,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAA;QACxF,CAAC;QAED,uCAAuC;QACvC,MAAM,UAAU,GAAG,aAAa,CAAC,MAAM,CAAA;QAEvC,gBAAgB;QAChB,IAAI,MAAM,EAAE,CAAC;YACZ,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;gBAC3B,QAAQ,MAAM,EAAE,CAAC;oBAChB,KAAK,QAAQ;wBACZ,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;oBACnB,KAAK,eAAe;wBACnB,OAAO,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,CAAA;oBAC/C,KAAK,YAAY;wBAChB,OAAO,CACN,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC;4BACjB,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC;4BAClB,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC;4BACpB,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC;4BACnB,CAAC,CAAC,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CACrF,CAAA;oBACF,KAAK,QAAQ,CAAC;oBACd;wBACC,OAAO,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAA;gBACpB,CAAC;YACF,CAAC,CAAC,CAAA;QACH,CAAC;aAAM,CAAC;YACP,yBAAyB;YACzB,aAAa,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC,CAAA;QAC1C,CAAC;QAED,yBAAyB;QACzB,MAAM,KAAK,GAAG,aAAa,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;YAC1C,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,IAAI,EAAE,IAAI,CAAC,IAAI;YACf,EAAE,EAAE,IAAI,CAAC,EAAE;YACX,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,KAAK;YACtC,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC;YACpB,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC;YAC9B,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC;YAC5B,SAAS,EAAE,IAAI,CAAC,SAAS,IAAI,CAAC;YAC9B,WAAW,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC;YAClC,UAAU,EAAE,IAAI,CAAC,UAAU,IAAI,CAAC;SAChC,CAAC,CAAC,CAAA;QAEH,OAAO;YACN,KAAK;YACL,UAAU;SACV,CAAA;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,0BAA0B,EAAE,KAAK,CAAC,CAAA;QAChD,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}