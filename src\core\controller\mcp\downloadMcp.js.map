{"version": 3, "file": "downloadMcp.js", "sourceRoot": "", "sources": ["downloadMcp.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAiB,MAAM,8BAA8B,CAAA;AAEnE,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAEhC;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,WAAW,CAAC,UAAsB,EAAE,OAAsB;IAC/E,IAAI,CAAC;QACJ,6BAA6B;QAC7B,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,oBAAoB,CAAC,CAAA;QACtC,CAAC;QAED,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAA;QAE3B,qDAAqD;QACrD,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,CAAA;QACrD,MAAM,WAAW,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,MAAiB,EAAE,EAAE,CAAC,MAAM,CAAC,IAAI,KAAK,KAAK,CAAC,CAAA;QAE9E,IAAI,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACxD,CAAC;QAED,wCAAwC;QACxC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,IAAI,CAChC,uCAAuC,EACvC,EAAE,KAAK,EAAE,EACT;YACC,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,OAAO,EAAE,KAAK;SACd,CACD,CAAA;QAED,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC;YACpB,MAAM,IAAI,KAAK,CAAC,2CAA2C,CAAC,CAAA;QAC7D,CAAC;QAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,EAAE,EAAE,QAAQ,EAAE,CAAC,CAAA;QAErE,MAAM,UAAU,GAAG,QAAQ,CAAC,IAAI,CAAA;QAEhC,2BAA2B;QAC3B,IAAI,CAAC,UAAU,CAAC,SAAS,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,6CAA6C,CAAC,CAAA;QAC/D,CAAC;QACD,IAAI,CAAC,UAAU,CAAC,aAAa,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,iDAAiD,CAAC,CAAA;QACnE,CAAC;QAED,0BAA0B;QAC1B,MAAM,UAAU,CAAC,oBAAoB,CAAC;YACrC,IAAI,EAAE,oBAAoB;YAC1B,kBAAkB,EAAE,UAAU;SAC9B,CAAC,CAAA;QAEF,wFAAwF;QACxF,MAAM,IAAI,GAAG,8BAA8B,UAAU,CAAC,SAAS;;SAExD,UAAU,CAAC,KAAK;;;;;;2DAMkC,UAAU,CAAC,aAAa,KAAK,UAAU,CAAC,uBAAuB,EAAE,CAAA;QAE1H,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,UAAU,CAAC,uBAAuB,EAAE,CAAA;QACnE,IAAI,YAAY,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAClC,MAAM,UAAU,CAAC,iCAAiC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC,CAAA;QACpE,CAAC;QAED,qCAAqC;QACrC,MAAM,UAAU,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA;QAC/B,MAAM,UAAU,CAAC,oBAAoB,CAAC;YACrC,IAAI,EAAE,QAAQ;YACd,MAAM,EAAE,mBAAmB;SAC3B,CAAC,CAAA;QAEF,yEAAyE;QACzE,OAAO,KAAK,CAAC,MAAM,EAAE,CAAA;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAA;QAC/C,IAAI,YAAY,GAAG,wBAAwB,CAAA;QAE3C,IAAI,KAAK,CAAC,YAAY,CAAC,KAAK,CAAC,EAAE,CAAC;YAC/B,IAAI,KAAK,CAAC,IAAI,KAAK,cAAc,EAAE,CAAC;gBACnC,YAAY,GAAG,sCAAsC,CAAA;YACtD,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC3C,YAAY,GAAG,sCAAsC,CAAA;YACtD,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG,EAAE,CAAC;gBAC3C,YAAY,GAAG,gDAAgD,CAAA;YAChE,CAAC;iBAAM,IAAI,CAAC,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,OAAO,EAAE,CAAC;gBAC7C,YAAY,GAAG,uDAAuD,CAAA;YACvE,CAAC;QACF,CAAC;aAAM,IAAI,KAAK,YAAY,KAAK,EAAE,CAAC;YACnC,YAAY,GAAG,KAAK,CAAC,OAAO,CAAA;QAC7B,CAAC;QAED,qDAAqD;QACrD,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,YAAY,CAAC,CAAA;QAC5C,MAAM,UAAU,CAAC,oBAAoB,CAAC;YACrC,IAAI,EAAE,oBAAoB;YAC1B,KAAK,EAAE,YAAY;SACnB,CAAC,CAAA;QAEF,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}