import * as vscode from "vscode"
import { TerminalInfo, TerminalRegistry } from "./TerminalRegistry"
import { InteractionStep } from "./InteractiveCommand"
import { DebugLogger } from "../../utils/debug-logger"

/**
 * Manages interactive command handling
 */
export class InteractionHandler {
  // Map of terminal ID to interaction handler data
  private interactionListeners: Map<number, {
    listener: vscode.Disposable
    timeoutId: NodeJS.Timeout
    currentStep: number
    totalSteps: number
  }> = new Map()

  /**
   * Sets up an interaction handler for a terminal
   * @param terminalInfo Terminal information
   * @param interactions Interaction steps
   * @returns Disposable for the interaction handler
   */
  public setupInteractionHandler(
    terminalInfo: TerminalInfo,
    interactions: InteractionStep[]
  ): vscode.Disposable {
    let currentStep = 0
    const handlerId = `interaction_${Date.now().toString(36)}`

    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] ====== 开始设置交互处理器 ======`)
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 终端ID: ${terminalInfo.id}`)
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 终端类型: ${terminalInfo.terminalType || 'unknown'}`)
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 交互步骤数: ${interactions.length}`)
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 开始时间: ${new Date().toISOString()}`)

    // 记录每个交互步骤的详细信息
    interactions.forEach((step, index) => {
      DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 步骤 ${index + 1}:
        - 提示: "${step.prompt}"
        - 响应: ${step.hidden ? "[隐藏]" : `"${step.response}"`}
        - 可选: ${step.optional || false}
        - 响应长度: ${step.response.length} 字符`)
    })

    // 监听终端输出 - 使用自定义事件
    // 注意：这里需要VS Code API支持，我们使用一个模拟的事件监听器
    const listener = {
      dispose: () => { /* 清理函数 */ }
    }

    // 模拟数据处理函数
    const handleData = (data: string) => {
      const timestamp = new Date().toISOString()

      // 检查是否已完成所有步骤
      if (currentStep >= interactions.length) {
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${timestamp}] 收到数据但所有步骤已完成，忽略`)
        return
      }

      // 记录收到的数据
      DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${timestamp}] 收到终端数据:`)
      DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 数据长度: ${data.length} 字符`)
      DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 数据预览: ${data.substring(0, 100)}${data.length > 100 ? '...' : ''}`)

      // 获取当前步骤
      const step = interactions[currentStep]
      DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 当前步骤: ${currentStep + 1}/${interactions.length}`)
      DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 正在匹配提示: "${step.prompt}"`)

      // 尝试匹配提示
      if (data.includes(step.prompt)) {
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${timestamp}] 成功匹配提示!`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 步骤 ${currentStep + 1}/${interactions.length} 匹配成功`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 匹配的提示: "${step.prompt}"`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 匹配的数据: "${data.substring(Math.max(0, data.indexOf(step.prompt) - 20), data.indexOf(step.prompt) + step.prompt.length + 20)}"`)
        console.log(`[DEBUG] Matched interaction prompt (${currentStep+1}/${interactions.length}): "${step.prompt}"`)

        // 准备发送响应
        const responseToLog = step.hidden ? '[隐藏内容]' : step.response
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 准备发送响应: ${responseToLog}`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 响应长度: ${step.response.length} 字符`)

        // 记录响应发送时间
        const responseTime = new Date().toISOString()
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${responseTime}] 发送响应`)

        // 模拟发送文本到终端
        console.log(`[MOCK] Sending text to terminal: ${step.response}`)
        // terminalInfo.terminal.sendText(step.response, !step.hidden)

        // 记录发送后的状态
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${new Date().toISOString()}] 响应已发送`)

        // 更新步骤计数
        currentStep++
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 步骤进度更新: ${currentStep}/${interactions.length}`)

        // 更新Map中的状态
        this.interactionListeners.set(terminalInfo.id, {
          listener,
          timeoutId,
          currentStep,
          totalSteps: interactions.length
        })
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 更新了交互状态映射`)

        // 检查是否完成所有步骤
        if (currentStep >= interactions.length) {
          const completionTime = new Date().toISOString()
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${completionTime}] ====== 所有交互步骤已完成 ======`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 终端ID: ${terminalInfo.id}`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 总步骤数: ${interactions.length}`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 完成时间: ${completionTime}`)

          // 记录完成状态
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 交互完成状态:`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] - 当前步骤: ${currentStep}`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] - 总步骤数: ${interactions.length}`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] - 终端类型: ${terminalInfo.terminalType || 'unknown'}`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] - 终端会话类型: ${terminalInfo.sessionType || 'unknown'}`)
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] - 终端忙碌状态: ${terminalInfo.busy ? '忙碌' : '空闲'}`)

          // 发送完成事件
          try {
            console.log(`[DEBUG] All interaction steps completed for terminal ${terminalInfo.id}`)
            // 这里可以添加一个事件发射器，通知其他组件交互已完成
            // this.emit('interaction-completed', terminalInfo.id)
          } catch (e) {
            DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 发送完成事件失败: ${e}`)
          }

          // 延迟清理
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 将在1秒后清理交互处理器`)
          setTimeout(() => {
            DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 执行清理操作`)
            this.cleanupInteractionHandler(terminalInfo.id)
            DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${new Date().toISOString()}] 清理完成`)
          }, 1000) // Delay cleanup to ensure last response is processed
        }
      } else {
        // 未匹配到提示
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${timestamp}] 未匹配到提示，继续等待`)
      }
    }

    // 设置全局超时
    const timeoutDuration = 30000; // 30秒超时
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 设置超时时间: ${timeoutDuration}ms`)

    const timeoutId = setTimeout(() => {
      const timeoutTimestamp = new Date().toISOString()

      if (currentStep < interactions.length) {
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] [${timeoutTimestamp}] ====== 交互超时 ======`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 终端ID: ${terminalInfo.id}`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 已完成步骤: ${currentStep}/${interactions.length}`)
        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 超时时间: ${timeoutDuration}ms`)

        // 记录未完成的步骤
        for (let i = currentStep; i < interactions.length; i++) {
          DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 未完成步骤 ${i + 1}: 提示="${interactions[i].prompt}"`)
        }

        console.log(`[DEBUG] Interaction timeout, not all steps completed (${currentStep}/${interactions.length})`)

        DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 执行超时清理`)
        this.cleanupInteractionHandler(terminalInfo.id)
      }
    }, timeoutDuration)

    // 存储监听器和超时ID
    this.interactionListeners.set(terminalInfo.id, {
      listener,
      timeoutId,
      currentStep,
      totalSteps: interactions.length
    })

    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] ====== 交互处理器设置完成 ======`)
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 终端ID: ${terminalInfo.id}`)
    DebugLogger.logTerminal(`[INTERACTION:${handlerId}] 准备就绪，等待终端输出...`)

    return listener
  }

  /**
   * Cleans up interaction handler for a terminal
   * @param terminalId Terminal ID
   */
  public cleanupInteractionHandler(terminalId: number): void {
    const cleanupId = `cleanup_${Date.now().toString(36)}`
    const timestamp = new Date().toISOString()

    DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] [${timestamp}] 开始清理终端 ${terminalId} 的交互处理器`)

    const handler = this.interactionListeners.get(terminalId)
    if (handler) {
      DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] 找到处理器，当前步骤: ${handler.currentStep}/${handler.totalSteps}`)

      // 清理资源
      DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] 释放监听器`)
      handler.listener.dispose()

      DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] 清除超时计时器`)
      clearTimeout(handler.timeoutId)

      // 从映射中删除
      this.interactionListeners.delete(terminalId)
      DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] 从映射中删除处理器`)

      console.log(`[DEBUG] Cleaned up interaction handler for terminal ${terminalId}`)
      DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] [${new Date().toISOString()}] 清理完成`)
    } else {
      DebugLogger.logTerminal(`[CLEANUP:${cleanupId}] 未找到终端 ${terminalId} 的处理器，无需清理`)
    }
  }

  /**
   * Handles interaction failure
   * @param terminalInfo Terminal information
   * @param command Command that failed
   * @param interactions Interaction steps
   * @param failedStep Index of the failed step
   */
  public handleInteractionFailure(
    terminalInfo: TerminalInfo,
    command: string,
    interactions: InteractionStep[],
    failedStep: number
  ): void {
    const failureId = `failure_${Date.now().toString(36)}`
    const timestamp = new Date().toISOString()

    DebugLogger.logTerminal(`[FAILURE:${failureId}] [${timestamp}] ====== 交互失败 ======`)
    DebugLogger.logTerminal(`[FAILURE:${failureId}] 终端ID: ${terminalInfo.id}`)
    DebugLogger.logTerminal(`[FAILURE:${failureId}] 命令: ${command}`)
    DebugLogger.logTerminal(`[FAILURE:${failureId}] 失败步骤: ${failedStep+1}/${interactions.length}`)
    DebugLogger.logTerminal(`[FAILURE:${failureId}] 终端类型: ${terminalInfo.terminalType || 'unknown'}`)

    console.log(`[DEBUG] Interaction failed, step ${failedStep+1}/${interactions.length}`)

    // 尝试重新发送当前步骤的响应
    if (failedStep < interactions.length) {
      const step = interactions[failedStep]

      DebugLogger.logTerminal(`[FAILURE:${failureId}] 失败步骤详情:`)
      DebugLogger.logTerminal(`[FAILURE:${failureId}] - 提示: "${step.prompt}"`)
      DebugLogger.logTerminal(`[FAILURE:${failureId}] - 响应: ${step.hidden ? "[隐藏]" : `"${step.response}"`}`)
      DebugLogger.logTerminal(`[FAILURE:${failureId}] - 可选: ${step.optional || false}`)

      if (!step.optional) {
        DebugLogger.logTerminal(`[FAILURE:${failureId}] 尝试重新发送响应`)
        console.log(`[DEBUG] Trying to resend response: "${step.response}"`)

        // 记录重试时间
        const retryTime = new Date().toISOString()
        DebugLogger.logTerminal(`[FAILURE:${failureId}] [${retryTime}] 重新发送响应`)

        terminalInfo.terminal.sendText(step.response, !step.hidden)
      } else {
        DebugLogger.logTerminal(`[FAILURE:${failureId}] 跳过可选步骤 ${failedStep+1}`)
        console.log(`[DEBUG] Skipping optional step ${failedStep+1}`)
        // 尝试继续下一步
      }
    } else {
      DebugLogger.logTerminal(`[FAILURE:${failureId}] 失败步骤超出范围，无法重试`)
    }

    // 记录失败并通知LLM
    DebugLogger.logTerminal(`[FAILURE:${failureId}] 记录失败信息到终端状态`)
    terminalInfo.lastInteractionFailure = {
      command,
      failedStep,
      stepPrompt: failedStep < interactions.length ? interactions[failedStep].prompt : undefined
    }

    DebugLogger.logTerminal(`[FAILURE:${failureId}] [${new Date().toISOString()}] 失败处理完成`)
  }

  /**
   * Gets the current interaction status for a terminal
   * @param terminalId Terminal ID
   * @returns Current interaction status or undefined if no interaction is in progress
   */
  public getInteractionStatus(terminalId: number): { current: number, total: number } | undefined {
    const statusId = `status_${Date.now().toString(36)}`

    DebugLogger.logTerminal(`[STATUS:${statusId}] 获取终端 ${terminalId} 的交互状态`)

    const handler = this.interactionListeners.get(terminalId)
    if (handler) {
      DebugLogger.logTerminal(`[STATUS:${statusId}] 找到处理器，当前步骤: ${handler.currentStep}/${handler.totalSteps}`)

      const status = {
        current: handler.currentStep,
        total: handler.totalSteps
      }

      DebugLogger.logTerminal(`[STATUS:${statusId}] 返回状态: ${JSON.stringify(status)}`)
      return status
    }

    DebugLogger.logTerminal(`[STATUS:${statusId}] 未找到终端 ${terminalId} 的处理器，返回undefined`)
    return undefined
  }
}
