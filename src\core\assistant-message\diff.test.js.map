{"version": 3, "file": "diff.test.js", "sourceRoot": "", "sources": ["diff.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,uBAAuB,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAA;AACzD,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,OAAO,CAAA;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAA;AAE7B,KAAK,UAAU,IAAI,CAAC,WAAmB,EAAE,eAAuB,EAAE,OAAgB;IACjF,OAAO,KAAK,CAAC,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;AAC1D,CAAC;AAED,QAAQ,CAAC,yBAAyB,EAAE,GAAG,EAAE;IACxC,MAAM,SAAS,GAAG;QACjB;YACC,IAAI,EAAE,YAAY;YAClB,QAAQ,EAAE,EAAE;YACZ,IAAI,EAAE;;;gBAGO;YACb,QAAQ,EAAE,eAAe;YACzB,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,uBAAuB;YAC7B,QAAQ,EAAE,aAAa;YACvB,IAAI,EAAE;;;gBAGO;YACb,QAAQ,EAAE,eAAe;YACzB,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,yBAAyB;YAC/B,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE;;;;gBAIO;YACb,QAAQ,EAAE,wBAAwB;YAClC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,gCAAgC;YACtC,QAAQ,EAAE,uBAAuB;YACjC,IAAI,EAAE;;;;gBAIO;YACb,QAAQ,EAAE,wBAAwB;YAClC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,gCAAgC;YACtC,QAAQ,EAAE,kCAAkC;YAC5C,IAAI,EAAE;;;;;;gBAMO;YACb,QAAQ,EAAE,wBAAwB;YAClC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,wBAAwB;YAC9B,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE;gBACL;;QAEI;gBACJ,YAAY;gBACZ,iBAAiB;aACjB,CAAC,IAAI,CAAC,IAAI,CAAC;YACZ,QAAQ,EAAE,0BAA0B;YACpC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,oCAAoC;YAC1C,QAAQ,EAAE,qBAAqB;YAC/B,IAAI,EAAE;;;;gBAIO;YACb,QAAQ,EAAE,wBAAwB;YAClC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,+BAA+B;YACrC,QAAQ,EAAE,8BAA8B;YACxC,IAAI,EAAE;;;;;;;;;;gBAUO;YACb,QAAQ,EAAE,0BAA0B;YACpC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,qBAAqB;YAC3B,QAAQ,EAAE,4BAA4B;YACtC,IAAI,EAAE;;;;;;;;;gBASO;YACb,QAAQ,EAAE,0BAA0B;YACpC,OAAO,EAAE,IAAI;SACb;QACD;YACC,IAAI,EAAE,qBAAqB;YAC3B,QAAQ,EAAE,4BAA4B;YACtC,IAAI,EAAE;;;;;;;;;gBASO;YACb,QAAQ,EAAE,wBAAwB;YAClC,OAAO,EAAE,IAAI;SACb;KACD,CAAA;IACD,+DAA+D;IAC/D,qDAAqD;IACrD,SAAS,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,EAAE,EAAE;QACjE,EAAE,CAAC,iBAAiB,IAAI,iBAAiB,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;YACnD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,OAAO,CAAC,CAAA;YACpD,MAAM,KAAK,GAAG,OAAO,KAAK,OAAO,CAAA;YACjC,MAAM,MAAM,GAAG,OAAO,KAAK,QAAQ,CAAA;YACnC,kDAAkD;YAClD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;YAEjC,iCAAiC;YACjC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACnC,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;QACvD,MAAM,QAAQ,GAAG,qBAAqB,CAAA;QACtC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QAEd,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YAChC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;QAED,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}