{"version": 3, "file": "toggleWindsurfRule.js", "sourceRoot": "", "sources": ["toggleWindsurfRule.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAGrF;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,kBAAkB,CAAC,UAAsB,EAAE,OAAkC;IAClG,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;IAErC,IAAI,CAAC,QAAQ,IAAI,OAAO,OAAO,KAAK,SAAS,EAAE,CAAC;QAC/C,OAAO,CAAC,KAAK,CAAC,mDAAmD,EAAE;YAClE,QAAQ;YACR,OAAO,EAAE,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO,OAAO,EAAE;SAC9E,CAAC,CAAA;QACF,MAAM,IAAI,KAAK,CAAC,sDAAsD,CAAC,CAAA;IACxE,CAAC;IAED,qBAAqB;IACrB,MAAM,OAAO,GAAI,CAAC,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,2BAA2B,CAAC,CAA0B,IAAI,EAAE,CAAA;IAC1H,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAA;IAC3B,MAAM,oBAAoB,CAAC,UAAU,CAAC,OAAO,EAAE,2BAA2B,EAAE,OAAO,CAAC,CAAA;IAEpF,8BAA8B;IAC9B,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,CAAA;AAC5B,CAAC"}