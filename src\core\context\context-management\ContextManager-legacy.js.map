{"version": 3, "file": "ContextManager-legacy.js", "sourceRoot": "", "sources": ["ContextManager-legacy.ts"], "names": [], "mappings": "AAGA,OAAO,EAAE,oBAAoB,EAAE,MAAM,wBAAwB,CAAA;AAE7D,MAAM,cAAc;IACnB,gCAAgC,CAC/B,sBAAyD,EACzD,aAA6B,EAC7B,GAAe,EACf,+BAA6D,EAC7D,mBAA2B;QAE3B,IAAI,sCAAsC,GAAG,KAAK,CAAA;QAElD,yJAAyJ;QACzJ,IAAI,mBAAmB,IAAI,CAAC,EAAE,CAAC;YAC9B,MAAM,eAAe,GAAG,aAAa,CAAC,mBAAmB,CAAC,CAAA;YAC1D,IAAI,eAAe,IAAI,eAAe,CAAC,IAAI,EAAE,CAAC;gBAC7C,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,GAAoB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,IAAI,CAAC,CAAA;gBAC1G,MAAM,WAAW,GAAG,CAAC,QAAQ,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,CAAC,GAAG,CAAC,UAAU,IAAI,CAAC,CAAC,CAAA;gBAC/F,MAAM,EAAE,cAAc,EAAE,GAAG,oBAAoB,CAAC,GAAG,CAAC,CAAA;gBAEpD,wFAAwF;gBACxF,IAAI,WAAW,IAAI,cAAc,EAAE,CAAC;oBACnC,+OAA+O;oBAC/O,qFAAqF;oBACrF,wKAAwK;oBACxK,MAAM,IAAI,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,MAAM,CAAA;oBAElE,wLAAwL;oBACxL,+BAA+B,GAAG,IAAI,CAAC,sBAAsB,CAC5D,sBAAsB,EACtB,+BAA+B,EAC/B,IAAI,CACJ,CAAA;oBAED,sCAAsC,GAAG,IAAI,CAAA;gBAC9C,CAAC;YACF,CAAC;QACF,CAAC;QAED,kJAAkJ;QAClJ,MAAM,4BAA4B,GAAG,IAAI,CAAC,oBAAoB,CAAC,sBAAsB,EAAE,+BAA+B,CAAC,CAAA;QAEvH,OAAO;YACN,+BAA+B,EAAE,+BAA+B;YAChE,sCAAsC,EAAE,sCAAsC;YAC9E,4BAA4B,EAAE,4BAA4B;SAC1D,CAAA;IACF,CAAC;IAEM,sBAAsB,CAC5B,WAA8C,EAC9C,mBAAiD,EACjD,IAAwB;QAExB,yIAAyI;QACzI,MAAM,eAAe,GAAG,CAAC,CAAA;QACzB,MAAM,WAAW,GAAG,mBAAmB,CAAC,CAAC,CAAC,mBAAmB,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;QAExE,IAAI,gBAAwB,CAAA;QAC5B,IAAI,IAAI,KAAK,MAAM,EAAE,CAAC;YACrB,gDAAgD;YAChD,uFAAuF;YACvF,kEAAkE;YAClE,qDAAqD;YACrD,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA,CAAC,mBAAmB;QAC9F,CAAC;aAAM,CAAC;YACP,+CAA+C;YAC/C,mFAAmF;YACnF,kEAAkE;YAClE,qDAAqD;YACrD,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,WAAW,CAAC,MAAM,GAAG,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,CAAA;QACpF,CAAC;QAED,IAAI,aAAa,GAAG,WAAW,GAAG,gBAAgB,GAAG,CAAC,CAAA;QAEtD,4MAA4M;QAC5M,6LAA6L;QAC7L,IAAI,WAAW,CAAC,aAAa,CAAC,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAChD,aAAa,IAAI,CAAC,CAAA;QACnB,CAAC;QAED,gFAAgF;QAChF,OAAO,CAAC,eAAe,EAAE,aAAa,CAAC,CAAA;IACxC,CAAC;IAEM,oBAAoB,CAC1B,QAA2C,EAC3C,YAA0C;QAE1C,IAAI,CAAC,YAAY,EAAE,CAAC;YACnB,OAAO,QAAQ,CAAA;QAChB,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,YAAY,CAAA;QACjC,uHAAuH;QACvH,8PAA8P;QAC9P,OAAO,CAAC,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,GAAG,QAAQ,CAAC,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC,CAAA;IACjE,CAAC;CACD"}