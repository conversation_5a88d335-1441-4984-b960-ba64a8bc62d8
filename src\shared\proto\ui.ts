// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: ui.proto

/* eslint-disable */
import { Empty, StringRequest } from "./common"

export const protobufPackage = "cline"

/** UiService provides methods for managing UI interactions */
export type UiServiceDefinition = typeof UiServiceDefinition
export const UiServiceDefinition = {
	name: "UiService",
	fullName: "cline.UiService",
	methods: {
		/** Scrolls to a specific settings section in the settings view */
		scrollToSettings: {
			name: "scrollToSettings",
			requestType: StringRequest,
			requestStream: false,
			responseType: Empty,
			responseStream: false,
			options: {},
		},
	},
} as const
