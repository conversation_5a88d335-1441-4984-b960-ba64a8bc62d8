{"version": 3, "file": "standalone.js", "sourceRoot": "", "sources": ["standalone.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,IAAI,MAAM,eAAe,CAAA;AACrC,OAAO,EAAE,iBAAiB,EAAE,MAAM,kBAAkB,CAAA;AACpD,OAAO,KAAK,MAAM,MAAM,mBAAmB,CAAA;AAE3C,OAAO,EAAE,QAAQ,EAAE,MAAM,cAAc,CAAA;AACvC,OAAO,EAAE,UAAU,EAAE,MAAM,oBAAoB,CAAA;AAC/C,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAC/E,OAAO,EAAE,iBAAiB,EAAE,KAAK,EAAE,GAAG,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,MAAM,SAAS,CAAA;AAE3F,OAAO,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAA;AAG5C,SAAS,IAAI;IACZ,GAAG,CAAC,qBAAqB,CAAC,CAAA;IAE1B,QAAQ,CAAC,gBAAgB,CAAC,CAAA;IAC1B,MAAM,UAAU,GAAG,IAAI,UAAU,CAAC,gBAAgB,EAAE,aAAa,EAAE,WAAW,CAAC,CAAA;IAC/E,MAAM,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,EAAE,CAAA;IAEhC,uBAAuB;IACvB,MAAM,UAAU,GAAG,IAAI,MAAM,CAAC,oBAAoB,CAAC,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAAA;IACrE,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IAE9B,gEAAgE;IAChE,WAAW,CAAC,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW,EAAE,4BAA4B,CAAC,CAAA;IAEjF,qBAAqB;IACrB,MAAM,UAAU,GAAG,IAAI,iBAAiB,CAAC,iBAAiB,CAAC,CAAA;IAC3D,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA;IAE9B,oBAAoB;IACpB,MAAM,IAAI,GAAG,iBAAiB,CAAA;IAC9B,MAAM,CAAC,SAAS,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB,CAAC,cAAc,EAAE,EAAE,CAAC,GAAG,EAAE,EAAE;QACvE,IAAI,GAAG,EAAE,CAAC;YACT,GAAG,CAAC,4BAA4B,IAAI,6BAA6B,GAAG,CAAC,OAAO,EAAE,CAAC,CAAA;YAC/E,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAA;QAChB,CAAC;aAAM,CAAC;YACP,MAAM,CAAC,KAAK,EAAE,CAAA;YACd,GAAG,CAAC,4BAA4B,IAAI,EAAE,CAAC,CAAA;QACxC,CAAC;IACF,CAAC,CAAC,CAAA;AACH,CAAC;AAED;;;;;;;;;;GAUG;AACH,SAAS,WAAW,CACnB,OAAyC,EACzC,UAAsB;IAEtB,OAAO,KAAK,EAAE,IAA+C,EAAE,QAAuC,EAAE,EAAE;QACzG,IAAI,CAAC;YACJ,GAAG,CAAC,iBAAiB,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YACtC,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,UAAU,EAAE,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAA;YACxE,qEAAqE;YACrE,0EAA0E;YAC1E,QAAQ,CAAC,IAAI,EAAE,gBAAgB,CAAC,MAAM,CAAC,CAAC,CAAA;QACzC,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YACnB,GAAG,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;YAC1D,QAAQ,CAAC;gBACR,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,gBAAgB;aACnB,CAAC,CAAA;QACxB,CAAC;IACF,CAAC,CAAA;AACF,CAAC;AAED,SAAS,4BAA4B,CACpC,OAA0D,EAC1D,UAAsB;IAEtB,OAAO,KAAK,EAAE,IAAoD,EAAE,EAAE;QACrE,IAAI,CAAC;YACJ,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,YAAY,CAAC,CAAC,GAAG,EAAE,EAAE,QAAQ,EAAE,CAAA;YACnE,GAAG,CAAC,2BAA2B,IAAI,CAAC,OAAO,EAAE,EAAE,CAAC,CAAA;YAEhD,MAAM,eAAe,GAA6B,CAAC,QAAQ,EAAE,MAAM,EAAE,cAAc,EAAE,EAAE;gBACtF,IAAI,CAAC;oBACJ,qEAAqE;oBACrE,0EAA0E;oBAC1E,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,QAAQ,CAAC,CAAC,CAAA,CAAC,sEAAsE;oBAE7G,IAAI,MAAM,KAAK,IAAI,EAAE,CAAC;wBACrB,GAAG,CAAC,sBAAsB,SAAS,EAAE,CAAC,CAAA;wBACtC,IAAI,CAAC,GAAG,EAAE,CAAA;oBACX,CAAC;oBACD,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;gBACzB,CAAC;gBAAC,OAAO,KAAK,EAAE,CAAC;oBAChB,OAAO,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBAC7B,CAAC;YACF,CAAC,CAAA;YACD,MAAM,OAAO,CAAC,UAAU,EAAE,gBAAgB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,eAAe,EAAE,SAAS,CAAC,CAAA;QACtF,CAAC;QAAC,OAAO,GAAQ,EAAE,CAAC;YACnB,GAAG,CAAC,uBAAuB,IAAI,CAAC,OAAO,EAAE,KAAK,GAAG,CAAC,KAAK,EAAE,CAAC,CAAA;YAC1D,IAAI,CAAC,OAAO,CAAC;gBACZ,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,QAAQ;gBAC1B,OAAO,EAAE,GAAG,CAAC,OAAO,IAAI,gBAAgB;aACnB,CAAC,CAAA;QACxB,CAAC;IACF,CAAC,CAAA;AACF,CAAC;AAED,IAAI,EAAE,CAAA"}