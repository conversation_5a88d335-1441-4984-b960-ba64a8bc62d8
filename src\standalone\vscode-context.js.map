{"version": 3, "file": "vscode-context.js", "sourceRoot": "", "sources": ["vscode-context.ts"], "names": [], "mappings": "AAAA,cAAc;AACd,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAEhC,OAAO,EAAE,GAAG,EAAE,MAAM,SAAS,CAAA;AAE7B,SAAS,OAAO,CAAC,IAAY;IAC5B,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,EAAE,CAAC,CAAA;IACvC,OAAO;QACN,MAAM,EAAE,IAAI;QACZ,MAAM,EAAE,EAAE;QACV,SAAS,EAAE,EAAE;QACb,IAAI,EAAE,EAAE;QACR,KAAK,EAAE,EAAE;QACT,QAAQ,EAAE,EAAE;QACZ,IAAI,EAAE,UAAU,MAMf;YACA,OAAO,OAAO,CAAC,IAAI,CAAC,CAAA;QACrB,CAAC;QACD,QAAQ,EAAE,UAAU,YAAsB;YACzC,OAAO,IAAI,CAAA;QACZ,CAAC;QACD,MAAM,EAAE;YACP,OAAO,EAAE,CAAA;QACV,CAAC;KACD,CAAA;AACF,CAAC;AAED,SAAS,aAAa;IACrB,MAAM,KAAK,GAAG,EAAE,CAAA;IAChB,OAAO;QACN,IAAI,EAAE;YACL,OAAO,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;QAC1B,CAAC;QACD,GAAG,EAAE,UAAa,GAAW;YAC5B,OAAO,GAAG,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAC7C,CAAC;QACD,MAAM,EAAE,UAAU,GAAW,EAAE,KAAU;YACxC,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAA;YAClB,OAAO,OAAO,CAAC,OAAO,EAAE,CAAA;QACzB,CAAC;KACD,CAAA;AACF,CAAC;AAED,MAAM,gBAAgB,GAA4B;IACjD,aAAa,EAAE,uBAAuB;IACtC,YAAY,EAAE,OAAO,CAAC,uBAAuB,CAAC;IAE9C,iBAAiB,EAAE,oBAAoB;IACvC,gBAAgB,EAAE,OAAO,CAAC,oBAAoB,CAAC;IAE/C,WAAW,EAAE,qBAAqB;IAClC,UAAU,EAAE,OAAO,CAAC,qBAAqB,CAAC;IAE1C,OAAO,EAAE,iBAAiB;IAC1B,MAAM,EAAE,OAAO,CAAC,iBAAiB,CAAC;IAElC,WAAW,EAAE,aAAa,EAAE;IAC5B,cAAc,EAAE,aAAa,EAAE;IAC/B,YAAY,EAAE,aAAa,EAAE;IAE7B,6BAA6B,EAAE;QAC9B,SAAS,EAAE,UAAU,KAAsC;YAC1D,OAAO;gBACN,UAAU,EAAE,KAAK;gBACjB,WAAW,EAAE,SAAS;gBACtB,OAAO,EAAE,UAAU,QAAgB,EAAE,KAAa,EAAE,OAAkD,IAAS,CAAC;gBAChH,MAAM,EAAE,UAAU,QAAgB,EAAE,KAAa,EAAE,OAAkD,IAAS,CAAC;gBAC/G,OAAO,EAAE,UAAU,QAAgB,EAAE,KAAa,EAAE,OAAkD,IAAS,CAAC;gBAChH,GAAG,EAAE,UAAU,QAAgB;oBAC9B,OAAO,SAAS,CAAA;gBACjB,CAAC;gBACD,OAAO,EAAE,UACR,QAIQ,EACR,OAAa,IACL,CAAC;gBACV,MAAM,EAAE,UAAU,QAAgB,IAAS,CAAC;gBAC5C,KAAK,EAAE,cAAmB,CAAC;gBAC3B,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;oBAKlB,MAAM,IAAI,KAAK,CAAC,kEAAkE,CAAC,CAAA;gBACpF,CAAC;aACD,CAAA;QACF,CAAC;QACD,UAAU,EAAE,KAAK;QACjB,WAAW,EAAE,SAAS;QACtB,OAAO,EAAE,UAAU,QAAgB,EAAE,KAAa,EAAE,OAAkD,IAAS,CAAC;QAChH,MAAM,EAAE,UAAU,QAAgB,EAAE,KAAa,EAAE,OAAkD,IAAS,CAAC;QAC/G,OAAO,EAAE,UAAU,QAAgB,EAAE,KAAa,EAAE,OAAkD,IAAS,CAAC;QAChH,GAAG,EAAE,UAAU,QAAgB;YAC9B,OAAO,SAAS,CAAA;QACjB,CAAC;QACD,OAAO,EAAE,UACR,QAIQ,EACR,OAAa;YAEb,MAAM,IAAI,KAAK,CAAC,uDAAuD,CAAC,CAAA;QACzE,CAAC;QACD,MAAM,EAAE,UAAU,QAAgB,IAAS,CAAC;QAC5C,KAAK,EAAE,cAAmB,CAAC;QAC3B,CAAC,MAAM,CAAC,QAAQ,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAA;QAC1E,CAAC;KACD;IAED,aAAa,EAAE,CAAC,EAAE,cAAc;IAEhC,SAAS,EAAE;QACV,EAAE,EAAE,mBAAmB;QACvB,QAAQ,EAAE,IAAI;QACd,aAAa,EAAE,uBAAuB;QACtC,YAAY,EAAE,OAAO,CAAC,uBAAuB,CAAC;QAC9C,WAAW,EAAE,EAAE;QACf,OAAO,EAAE,EAAE;QACX,QAAQ,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACxB,aAAa,EAAE,MAAM,CAAC,aAAa,CAAC,EAAE;KACtC;IAED,aAAa,EAAE,EAAE;IAEjB,cAAc,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,yBAAyB,OAAO,EAAE;IAE/D,OAAO,EAAE;QACR,KAAK,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACrB,GAAG,EAAE,KAAK,IAAI,EAAE,CAAC,SAAS;QAC1B,MAAM,EAAE,KAAK,IAAI,EAAE,GAAE,CAAC;QACtB,WAAW,EAAE,EAAE;KACf;CACD,CAAA;AAED,MAAM,aAAa,GAAyB;IAC3C,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;IAC5C,UAAU,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC;IACvC,KAAK,EAAE,GAAG,EAAE,GAAE,CAAC;IACf,IAAI,EAAE,GAAG,EAAE,GAAE,CAAC;IACd,IAAI,EAAE,GAAG,EAAE,GAAE,CAAC;IACd,OAAO,EAAE,GAAG,EAAE,GAAE,CAAC;IACjB,IAAI,EAAE,EAAE;IACR,OAAO,EAAE,UAAU,KAAa,IAAS,CAAC;CAC1C,CAAA;AAED,SAAS,WAAW,CAAC,OAAyB;IAC7C,GAAG,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAA;IACnC,OAAO,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,CAAA;AAC7B,CAAC;AAED,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC,CAAA;AAEjD,OAAO,EAAE,gBAAgB,EAAE,aAAa,EAAE,WAAW,EAAE,CAAA"}