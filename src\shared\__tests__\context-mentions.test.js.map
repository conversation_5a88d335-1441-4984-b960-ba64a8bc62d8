{"version": 3, "file": "context-mentions.test.js", "sourceRoot": "", "sources": ["context-mentions.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAA;AAE7B,OAAO,EAAE,YAAY,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAA;AAOtE,SAAS,WAAW,CAAC,KAAa,EAAE,QAAuB;IAC1D,MAAM,KAAK,GAAG,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA;IACtC,OAAO;QACN,MAAM,EAAE,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;QAC/B,QAAQ;KACR,CAAA;AACF,CAAC;AAED,SAAS,WAAW,CAAC,MAAkB;IACtC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAA;IACzC,OAAO,IAAI,CAAA;AACZ,CAAC;AAED,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;IAC9B,QAAQ,CAAC,sBAAsB,EAAE,GAAG,EAAE;QACrC,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAA4B;gBACtC,CAAC,wBAAwB,EAAE,wBAAwB,CAAC;gBACpD,CAAC,gBAAgB,EAAE,gBAAgB,CAAC;aACpC,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAA4B;gBACtC,CAAC,4CAA4C,EAAE,4CAA4C,CAAC;gBAC5F,CAAC,6BAA6B,EAAE,6BAA6B,CAAC;aAC9D,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;QACvC,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC7B,MAAM,KAAK,GAA4B;gBACtC,CAAC,sBAAsB,EAAE,sBAAsB,CAAC;gBAChD,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;gBAC5C,CAAC,aAAa,EAAE,aAAa,CAAC;aAC9B,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YACvB,MAAM,KAAK,GAA4B;gBACtC,CAAC,qBAAqB,EAAE,qBAAqB,CAAC;gBAC9C,CAAC,wCAAwC,EAAE,wCAAwC,CAAC;gBACpF,CAAC,oCAAoC,EAAE,oCAAoC,CAAC;aAC5E,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,oBAAoB,EAAE,GAAG,EAAE;YAC7B,MAAM,KAAK,GAA4B;gBACtC,CAAC,2CAA2C,EAAE,2CAA2C,CAAC;aAC1F,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACnC,MAAM,KAAK,GAA4B;gBACtC,CAAC,WAAW,EAAE,WAAW,CAAC;gBAC1B,CAAC,cAAc,EAAE,cAAc,CAAC;gBAChC,CAAC,WAAW,EAAE,WAAW,CAAC;aAC1B,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QACjC,EAAE,CAAC,0BAA0B,EAAE,GAAG,EAAE;YACnC,MAAM,KAAK,GAA0B;gBACpC,CAAC,sBAAsB,EAAE,IAAI,CAAC;gBAC9B,CAAC,GAAG,EAAE,IAAI,CAAC;gBACX,CAAC,gBAAgB,EAAE,IAAI,CAAC;aACxB,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;YAChD,MAAM,MAAM,GAAG,WAAW,CAAC,uCAAuC,EAAE,wBAAwB,CAAC,CAAA;YAC7F,WAAW,CAAC,MAAM,CAAC,CAAA;QACpB,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,YAAY,EAAE,GAAG,EAAE;QAC3B,EAAE,CAAC,8BAA8B,EAAE,GAAG,EAAE;YACvC,MAAM,KAAK,GAA4B;gBACtC,CAAC,uDAAuD,EAAE,wBAAwB,CAAC;gBACnF,CAAC,oCAAoC,EAAE,WAAW,CAAC;gBACnD,CAAC,yDAAyD,EAAE,aAAa,CAAC;aAC1E,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,mDAAmD,EAAE,GAAG,EAAE;YAC5D,MAAM,IAAI,GAAG,6FAA6F,CAAA;YAC1G,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;YAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,kBAAkB,EAAE,yBAAyB,EAAE,WAAW,EAAE,cAAc,CAAC,CAAC,CAAA;QACtG,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,0CAA0C,EAAE,GAAG,EAAE;YACnD,MAAM,KAAK,GAA4B;gBACtC,CAAC,sCAAsC,EAAE,sCAAsC,CAAC;gBAChF,CAAC,+CAA+C,EAAE,+CAA+C,CAAC;gBAClG,CAAC,mCAAmC,EAAE,mCAAmC,CAAC;gBAC1E,CAAC,oCAAoC,EAAE,oCAAoC,CAAC;aAC5E,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAClD,EAAE,CAAC,0EAA0E,EAAE,GAAG,EAAE;YACnF,MAAM,IAAI,GAAG,6DAA6D,CAAA;YAC1E,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;YAC5C,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,aAAa,CAAC,CAAA;YAEnC,2CAA2C;YAC3C,MAAM,iBAAiB,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA;YAC9C,MAAM,YAAY,GAAG,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAI,EAAE,CAAA;YAC/E,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,qBAAqB,CAAC,CAAA;QAClD,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,6BAA6B,EAAE,GAAG,EAAE;QAC5C,EAAE,CAAC,2CAA2C,EAAE,GAAG,EAAE;YACpD,MAAM,KAAK,GAA4B;gBACtC,CAAC,uBAAuB,EAAE,uBAAuB,CAAC;gBAClD,CAAC,+BAA+B,EAAE,+BAA+B,CAAC;gBAClE,CAAC,oBAAoB,EAAE,oBAAoB,CAAC;gBAC5C,CAAC,4BAA4B,EAAE,4BAA4B,CAAC;aAC5D,CAAA;YAED,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE,EAAE;gBACnC,MAAM,MAAM,GAAG,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,CAAA;gBAC3C,WAAW,CAAC,MAAM,CAAC,CAAA;YACpB,CAAC,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}