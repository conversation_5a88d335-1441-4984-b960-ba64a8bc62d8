{"version": 3, "file": "grpc-handler.js", "sourceRoot": "", "sources": ["grpc-handler.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,eAAe,EAAE,MAAM,uBAAuB,CAAA;AACvD,OAAO,EAAE,mBAAmB,EAAE,MAAM,yBAAyB,CAAA;AAO7D;;GAEG;AACH,MAAM,OAAO,WAAW;IACH;IAApB,YAAoB,UAAsB;QAAtB,eAAU,GAAV,UAAU,CAAY;IAAG,CAAC;IAE9C;;;;;;;;OAQG;IACH,KAAK,CAAC,aAAa,CAClB,OAAe,EACf,MAAc,EACd,OAAY,EACZ,SAAiB,EACjB,cAAuB,KAAK;QAM5B,IAAI,CAAC;YACJ,4DAA4D;YAC5D,IAAI,WAAW,EAAE,CAAC;gBACjB,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAA;gBACtE,OAAM;YACP,CAAC;YAED,0CAA0C;YAC1C,MAAM,aAAa,GAAG,eAAe,CAAC,OAAO,CAAC,CAAA;YAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAA;YAC/C,CAAC;YAED,uBAAuB;YACvB,OAAO;gBACN,OAAO,EAAE,MAAM,aAAa,CAAC,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,CAAC;gBAC7E,UAAU,EAAE,SAAS;aACrB,CAAA;QACF,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO;gBACN,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,UAAU,EAAE,SAAS;aACrB,CAAA;QACF,CAAC;IACF,CAAC;IAED;;;;;;OAMG;IACK,KAAK,CAAC,sBAAsB,CAAC,OAAe,EAAE,MAAc,EAAE,OAAY,EAAE,SAAiB;QACpG,oCAAoC;QACpC,MAAM,cAAc,GAA6B,KAAK,EACrD,QAAa,EACb,SAAkB,KAAK,EACvB,cAAuB,EACtB,EAAE;YACH,MAAM,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;gBAC1C,IAAI,EAAE,eAAe;gBACrB,aAAa,EAAE;oBACd,OAAO,EAAE,QAAQ;oBACjB,UAAU,EAAE,SAAS;oBACrB,YAAY,EAAE,CAAC,MAAM;oBACrB,eAAe,EAAE,cAAc;iBAC/B;aACD,CAAC,CAAA;QACH,CAAC,CAAA;QAED,IAAI,CAAC;YACJ,0CAA0C;YAC1C,MAAM,aAAa,GAAG,eAAe,CAAC,OAAO,CAAC,CAAA;YAC9C,IAAI,CAAC,aAAa,EAAE,CAAC;gBACpB,MAAM,IAAI,KAAK,CAAC,oBAAoB,OAAO,EAAE,CAAC,CAAA;YAC/C,CAAC;YAED,0CAA0C;YAC1C,IAAI,CAAC,aAAa,CAAC,gBAAgB,EAAE,CAAC;gBACrC,MAAM,IAAI,KAAK,CAAC,WAAW,OAAO,6BAA6B,CAAC,CAAA;YACjE,CAAC;YAED,4EAA4E;YAC5E,MAAM,aAAa,CAAC,gBAAgB,CAAC,IAAI,CAAC,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,CAAC,CAAA;YAEjG,mFAAmF;YACnF,+FAA+F;QAChG,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,sBAAsB;YACtB,MAAM,IAAI,CAAC,UAAU,CAAC,oBAAoB,CAAC;gBAC1C,IAAI,EAAE,eAAe;gBACrB,aAAa,EAAE;oBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;oBAC7D,UAAU,EAAE,SAAS;oBACrB,YAAY,EAAE,KAAK;iBACnB;aACD,CAAC,CAAA;QACH,CAAC;IACF,CAAC;CACD;AAED,qEAAqE;AACrE,MAAM,eAAe,GAAG,IAAI,mBAAmB,EAAE,CAAA;AAEjD;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CACtC,UAAsB,EACtB,OAMC;IAED,IAAI,CAAC;QACJ,MAAM,WAAW,GAAG,IAAI,WAAW,CAAC,UAAU,CAAC,CAAA;QAE/C,2EAA2E;QAC3E,IAAI,OAAO,CAAC,YAAY,EAAE,CAAC;YAC1B,IAAI,CAAC;gBACJ,MAAM,WAAW,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,MAAM,EAAE,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,UAAU,EAAE,IAAI,CAAC,CAAA;YAC5G,CAAC;oBAAS,CAAC;gBACV,qDAAqD;gBACrD,mEAAmE;YACpE,CAAC;YACD,OAAM;QACP,CAAC;QAED,yDAAyD;QACzD,MAAM,QAAQ,GAAG,CAAC,MAAM,WAAW,CAAC,aAAa,CAChD,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,MAAM,EACd,OAAO,CAAC,OAAO,EACf,OAAO,CAAC,UAAU,EAClB,KAAK,CACL,CAIA,CAAA;QAED,wCAAwC;QACxC,MAAM,UAAU,CAAC,oBAAoB,CAAC;YACrC,IAAI,EAAE,eAAe;YACrB,aAAa,EAAE,QAAQ;SACvB,CAAC,CAAA;IACH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,sBAAsB;QACtB,MAAM,UAAU,CAAC,oBAAoB,CAAC;YACrC,IAAI,EAAE,eAAe;YACrB,aAAa,EAAE;gBACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,UAAU,EAAE,OAAO,CAAC,UAAU;aAC9B;SACD,CAAC,CAAA;IACH,CAAC;AACF,CAAC;AAED;;;;GAIG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC5C,UAAsB,EACtB,OAEC;IAED,MAAM,SAAS,GAAG,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,UAAU,CAAC,CAAA;IAEnE,IAAI,SAAS,EAAE,CAAC;QACf,mCAAmC;QACnC,MAAM,UAAU,CAAC,oBAAoB,CAAC;YACrC,IAAI,EAAE,eAAe;YACrB,aAAa,EAAE;gBACd,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE;gBAC5B,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,YAAY,EAAE,KAAK;aACnB;SACD,CAAC,CAAA;IACH,CAAC;SAAM,CAAC;QACP,OAAO,CAAC,GAAG,CAAC,+CAA+C,OAAO,CAAC,UAAU,EAAE,CAAC,CAAA;IACjF,CAAC;AACF,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,kBAAkB;IACjC,OAAO,eAAe,CAAA;AACvB,CAAC"}