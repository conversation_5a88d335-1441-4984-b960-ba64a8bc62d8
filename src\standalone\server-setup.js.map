{"version": 3, "file": "server-setup.js", "sourceRoot": "", "sources": ["server-setup.ts"], "names": [], "mappings": "AAMA,kBAAkB;AAClB,OAAO,EAAE,mBAAmB,EAAE,MAAM,gDAAgD,CAAA;AACpF,OAAO,EAAE,oBAAoB,EAAE,MAAM,iDAAiD,CAAA;AAEtF,kBAAkB;AAClB,OAAO,EAAE,wBAAwB,EAAE,MAAM,qDAAqD,CAAA;AAC9F,OAAO,EAAE,qBAAqB,EAAE,MAAM,kDAAkD,CAAA;AACxF,OAAO,EAAE,eAAe,EAAE,MAAM,4CAA4C,CAAA;AAC5E,OAAO,EAAE,qBAAqB,EAAE,MAAM,kDAAkD,CAAA;AACxF,OAAO,EAAE,qBAAqB,EAAE,MAAM,kDAAkD,CAAA;AACxF,OAAO,EAAE,uBAAuB,EAAE,MAAM,oDAAoD,CAAA;AAE5F,sBAAsB;AACtB,OAAO,EAAE,cAAc,EAAE,MAAM,+CAA+C,CAAA;AAC9E,OAAO,EAAE,iBAAiB,EAAE,MAAM,kDAAkD,CAAA;AAEpF,eAAe;AACf,OAAO,EAAE,eAAe,EAAE,MAAM,yCAAyC,CAAA;AACzE,OAAO,EAAE,QAAQ,EAAE,MAAM,kCAAkC,CAAA;AAC3D,OAAO,EAAE,SAAS,EAAE,MAAM,mCAAmC,CAAA;AAC7D,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAA;AACjE,OAAO,EAAE,cAAc,EAAE,MAAM,wCAAwC,CAAA;AACvE,OAAO,EAAE,cAAc,EAAE,MAAM,wCAAwC,CAAA;AACvE,OAAO,EAAE,aAAa,EAAE,MAAM,uCAAuC,CAAA;AACrE,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AACnE,OAAO,EAAE,gBAAgB,EAAE,MAAM,0CAA0C,CAAA;AAC3E,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAA;AACjE,OAAO,EAAE,eAAe,EAAE,MAAM,yCAAyC,CAAA;AACzE,OAAO,EAAE,gBAAgB,EAAE,MAAM,0CAA0C,CAAA;AAC3E,OAAO,EAAE,kBAAkB,EAAE,MAAM,4CAA4C,CAAA;AAC/E,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AAEnE,cAAc;AACd,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAA;AACxE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yCAAyC,CAAA;AAC1E,OAAO,EAAE,kBAAkB,EAAE,MAAM,2CAA2C,CAAA;AAC9E,OAAO,EAAE,WAAW,EAAE,MAAM,oCAAoC,CAAA;AAChE,OAAO,EAAE,gBAAgB,EAAE,MAAM,yCAAyC,CAAA;AAC1E,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAA;AACxE,OAAO,EAAE,qBAAqB,EAAE,MAAM,8CAA8C,CAAA;AACpF,OAAO,EAAE,qBAAqB,EAAE,MAAM,8CAA8C,CAAA;AAEpF,iBAAiB;AACjB,OAAO,EAAE,eAAe,EAAE,MAAM,2CAA2C,CAAA;AAC3E,OAAO,EAAE,iBAAiB,EAAE,MAAM,6CAA6C,CAAA;AAC/E,OAAO,EAAE,iBAAiB,EAAE,MAAM,6CAA6C,CAAA;AAC/E,OAAO,EAAE,uBAAuB,EAAE,MAAM,mDAAmD,CAAA;AAC3F,OAAO,EAAE,mBAAmB,EAAE,MAAM,+CAA+C,CAAA;AACnF,OAAO,EAAE,qBAAqB,EAAE,MAAM,iDAAiD,CAAA;AAEvF,gBAAgB;AAChB,OAAO,EAAE,SAAS,EAAE,MAAM,oCAAoC,CAAA;AAC9D,OAAO,EAAE,QAAQ,EAAE,MAAM,mCAAmC,CAAA;AAE5D,gBAAgB;AAChB,OAAO,EAAE,cAAc,EAAE,MAAM,yCAAyC,CAAA;AACxE,OAAO,EAAE,gBAAgB,EAAE,MAAM,2CAA2C,CAAA;AAC5E,OAAO,EAAE,mBAAmB,EAAE,MAAM,8CAA8C,CAAA;AAClF,OAAO,EAAE,UAAU,EAAE,MAAM,qCAAqC,CAAA;AAChE,OAAO,EAAE,iBAAiB,EAAE,MAAM,4CAA4C,CAAA;AAC9E,OAAO,EAAE,+BAA+B,EAAE,MAAM,0DAA0D,CAAA;AAE1G,eAAe;AACf,OAAO,EAAE,UAAU,EAAE,MAAM,oCAAoC,CAAA;AAC/D,OAAO,EAAE,SAAS,EAAE,MAAM,mCAAmC,CAAA;AAC7D,OAAO,EAAE,kBAAkB,EAAE,MAAM,4CAA4C,CAAA;AAC/E,OAAO,EAAE,OAAO,EAAE,MAAM,iCAAiC,CAAA;AACzD,OAAO,EAAE,cAAc,EAAE,MAAM,wCAAwC,CAAA;AACvE,OAAO,EAAE,gBAAgB,EAAE,MAAM,0CAA0C,CAAA;AAC3E,OAAO,EAAE,kBAAkB,EAAE,MAAM,4CAA4C,CAAA;AAC/E,OAAO,EAAE,uBAAuB,EAAE,MAAM,iDAAiD,CAAA;AACzF,OAAO,EAAE,cAAc,EAAE,MAAM,wCAAwC,CAAA;AACvE,OAAO,EAAE,WAAW,EAAE,MAAM,qCAAqC,CAAA;AACjE,OAAO,EAAE,YAAY,EAAE,MAAM,sCAAsC,CAAA;AACnE,OAAO,EAAE,yBAAyB,EAAE,MAAM,mDAAmD,CAAA;AAE7F,aAAa;AACb,OAAO,EAAE,gBAAgB,EAAE,MAAM,wCAAwC,CAAA;AAEzE,cAAc;AACd,OAAO,EAAE,eAAe,EAAE,MAAM,wCAAwC,CAAA;AACxE,OAAO,EAAE,kBAAkB,EAAE,MAAM,2CAA2C,CAAA;AAE9E,MAAM,UAAU,WAAW,CAC1B,MAAmB,EACnB,KAAU,EACV,UAAsB,EACtB,OAA2B,EAC3B,qBAA0D;IAE1D,kBAAkB;IAClB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE;QACrD,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC;QAC7D,oBAAoB,EAAE,OAAO,CAAC,oBAAoB,EAAE,UAAU,CAAC;KAC/D,CAAC,CAAA;IAEF,kBAAkB;IAClB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,cAAc,CAAC,OAAO,EAAE;QACrD,wBAAwB,EAAE,OAAO,CAAC,wBAAwB,EAAE,UAAU,CAAC;QACvE,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;QACjE,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;QACjE,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;QACjE,uBAAuB,EAAE,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC;KACrE,CAAC,CAAA;IAEF,sBAAsB;IACtB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,kBAAkB,CAAC,OAAO,EAAE;QACzD,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC;QACnD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC;KACzD,CAAC,CAAA;IAEF,eAAe;IACf,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE;QAClD,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC;QACvC,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;QACzC,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;QAC7C,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC;QACnD,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC;QACnD,aAAa,EAAE,OAAO,CAAC,aAAa,EAAE,UAAU,CAAC;QACjD,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;QAC/C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;QACvD,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;QAC7C,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;QACvD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC;QAC3D,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;KAC/C,CAAC,CAAA;IAEF,cAAc;IACd,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;QACjD,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;QACvD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC;QAC3D,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;QAC7C,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;QACvD,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;QACjE,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;KACjE,CAAC,CAAA;IAEF,iBAAiB;IACjB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,OAAO,EAAE;QACpD,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC;QACzD,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC;QACzD,uBAAuB,EAAE,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC;QACrE,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC;QAC7D,qBAAqB,EAAE,OAAO,CAAC,qBAAqB,EAAE,UAAU,CAAC;KACjE,CAAC,CAAA;IAEF,gBAAgB;IAChB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE;QACnD,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;QACzC,QAAQ,EAAE,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC;KACvC,CAAC,CAAA;IAEF,gBAAgB;IAChB,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,YAAY,CAAC,OAAO,EAAE;QACnD,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC;QACnD,gBAAgB,EAAE,qBAAqB,CAAC,gBAAgB,EAAE,UAAU,CAAC;QACrE,mBAAmB,EAAE,OAAO,CAAC,mBAAmB,EAAE,UAAU,CAAC;QAC7D,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;QAC3C,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,EAAE,UAAU,CAAC;QACzD,+BAA+B,EAAE,OAAO,CAAC,+BAA+B,EAAE,UAAU,CAAC;KACrF,CAAC,CAAA;IAEF,eAAe;IACf,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC,OAAO,EAAE;QAClD,UAAU,EAAE,OAAO,CAAC,UAAU,EAAE,UAAU,CAAC;QAC3C,SAAS,EAAE,OAAO,CAAC,SAAS,EAAE,UAAU,CAAC;QACzC,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC;QAC3D,OAAO,EAAE,OAAO,CAAC,OAAO,EAAE,UAAU,CAAC;QACrC,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC;QACnD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;QACvD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC;QAC3D,uBAAuB,EAAE,OAAO,CAAC,uBAAuB,EAAE,UAAU,CAAC;QACrE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,UAAU,CAAC;QACnD,WAAW,EAAE,OAAO,CAAC,WAAW,EAAE,UAAU,CAAC;QAC7C,YAAY,EAAE,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC;QAC/C,yBAAyB,EAAE,OAAO,CAAC,yBAAyB,EAAE,UAAU,CAAC;KACzE,CAAC,CAAA;IAEF,aAAa;IACb,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,SAAS,CAAC,OAAO,EAAE;QAChD,gBAAgB,EAAE,OAAO,CAAC,gBAAgB,EAAE,UAAU,CAAC;KACvD,CAAC,CAAA;IAEF,cAAc;IACd,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,OAAO,EAAE;QACjD,eAAe,EAAE,OAAO,CAAC,eAAe,EAAE,UAAU,CAAC;QACrD,kBAAkB,EAAE,OAAO,CAAC,kBAAkB,EAAE,UAAU,CAAC;KAC3D,CAAC,CAAA;AACH,CAAC"}