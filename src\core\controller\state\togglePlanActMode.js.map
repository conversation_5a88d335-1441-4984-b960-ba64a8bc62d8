{"version": 3, "file": "togglePlanActMode.js", "sourceRoot": "", "sources": ["togglePlanActMode.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,KAAK,EAAE,MAAM,8BAA8B,CAAA;AAEpD,OAAO,EACN,oCAAoC,EACpC,sCAAsC,GACtC,MAAM,0DAA0D,CAAA;AAEjE;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,iBAAiB,CAAC,UAAsB,EAAE,OAAiC;IAChG,IAAI,CAAC;QACJ,IAAI,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC;YAC3B,MAAM,IAAI,KAAK,CAAC,4BAA4B,CAAC,CAAA;QAC9C,CAAC;QAED,MAAM,YAAY,GAAG,sCAAsC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QACjF,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,oCAAoC,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,SAAS,CAAA;QAE/G,8CAA8C;QAC9C,MAAM,UAAU,CAAC,iCAAiC,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;QAE7E,OAAO,KAAK,CAAC,MAAM,EAAE,CAAA;IACtB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAA;QACvD,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}