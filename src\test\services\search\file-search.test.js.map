{"version": 3, "file": "file-search.test.js", "sourceRoot": "", "sources": ["file-search.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,OAAO,CAAA;AACpC,OAAO,MAAM,MAAM,QAAQ,CAAA;AAC3B,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,EAAE,QAAQ,EAAE,MAAM,QAAQ,CAAA;AAGjC,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAChC,OAAO,KAAK,EAAE,MAAM,IAAI,CAAA;AACxB,OAAO,KAAK,UAAU,MAAM,8BAA8B,CAAA;AAC1D,OAAO,KAAK,OAAO,MAAM,mBAAmB,CAAA;AAE5C,QAAQ,CAAC,aAAa,EAAE;IACvB,IAAI,OAA2B,CAAA;IAC/B,IAAI,SAA0B,CAAA;IAE9B,UAAU,CAAC;QACV,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAA;QAC/B,SAAS,GAAG,OAAO,CAAC,IAAI,EAAE,CAAA;QAE1B,6EAA6E;QAC7E,MAAM,YAAY,GAA8B,UAAU,OAAO,EAAE,OAAO;YACzE,OAAO,SAAS,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QACnC,CAAC,CAAA;QAED,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,kBAAkB,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAA;QAClE,8EAA8E;QAC9E,OAAO,CAAC,aAAa,CAAC,MAAM,CAAC,GAAG,EAAE,SAAS,EAAE,GAAG,EAAE,CAAC,eAAe,CAAC,CAAA;QACnE,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,WAAW,EAAE,GAAG,EAAE,CAAC,KAAK,EAAc,CAAC,CAAA;QACrF,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,mBAAmB,CAAC,CAAA;IAClE,CAAC,CAAC,CAAA;IAEF,SAAS,CAAC;QACT,OAAO,CAAC,OAAO,EAAE,CAAA;IAClB,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,wBAAwB,EAAE;QAClC,EAAE,CAAC,6DAA6D,EAAE,KAAK;YACtE,MAAM,SAAS,GAAG,CAAC,WAAW,EAAE,kBAAkB,EAAE,4BAA4B,CAAC,CAAA;YAEjF,6CAA6C;YAC7C,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC;gBAC/B,IAAI;oBACH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;oBAC/B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,+BAA+B;gBAChD,CAAC;aACD,CAAC,CAAA;YAEF,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC;gBAC/B,IAAI;oBACH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,eAAe;gBAChC,CAAC;aACD,CAAC,CAAA;YAEF,SAAS,CAAC,OAAO,CAAC;gBACjB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,UAAU;gBAClB,EAAE,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,EAAE,CAAC;aACY,CAAC,CAAA;YAE1C,qFAAqF;YACrF,+CAA+C;YAC/C,MAAM,cAAc,GAAgE;gBACnF,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE;gBACvD,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;gBAC7D,EAAE,IAAI,EAAE,4BAA4B,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;gBACvE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;gBACrD,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;aACjE,CAAA;YAED,+CAA+C;YAC/C,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAA;YAE3E,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,sBAAsB,CAAC,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,CAAA;YAEvF,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAC5B,kDAAkD;YAElD,MAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM,CAAC,CAAA;YAC3D,MAAM,OAAO,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAA;YAE/D,yDAAyD;YACzD,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAA;YAC7C,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,kBAAkB,CAAC,CAAC,CAAC,CAAA;YAE/C,MAAM,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;gBAChC,IAAI,EAAE,WAAW;gBACjB,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,WAAW;aAClB,CAAC,CAAA;YAEF,MAAM,CAAC,OAAO,CAAC,CAAC,WAAW,CAAC;gBAC3B,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;gBACrD,EAAE,IAAI,EAAE,mBAAmB,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,WAAW,EAAE;aACjE,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,mCAAmC,EAAE,KAAK;YAC5C,MAAM,SAAS,GAAG,oBAAoB,CAAA;YAEtC,4CAA4C;YAC5C,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC;gBAC/B,IAAI;oBACH,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,eAAe;gBAChC,CAAC;aACD,CAAC,CAAA;YAEF,MAAM,UAAU,GAAG,IAAI,QAAQ,CAAC;gBAC/B,IAAI;oBACH,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;oBACpB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,CAAC,+BAA+B;gBAChD,CAAC;aACD,CAAC,CAAA;YAEF,SAAS,CAAC,OAAO,CAAC;gBACjB,MAAM,EAAE,UAAU;gBAClB,MAAM,EAAE,UAAU;gBAClB,EAAE,EAAE,UAAU,KAAa,EAAE,QAAkB;oBAC9C,IAAI,KAAK,KAAK,OAAO,EAAE,CAAC;wBACvB,QAAQ,CAAC,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,CAAA;oBAC/B,CAAC;oBACD,OAAO,IAAI,CAAA;gBACZ,CAAC;aACuC,CAAC,CAAA;YAE1C,MAAM,MAAM,CAAC,UAAU,CAAC,sBAAsB,CAAC,WAAW,EAAE,YAAY,EAAE,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,YAAY,CAC/F,0BAA0B,SAAS,EAAE,CACrC,CAAA;QACF,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,sBAAsB,EAAE;QAChC,EAAE,CAAC,6CAA6C,EAAE,KAAK;YACtD,MAAM,SAAS,GAAgE;gBAC9E,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE;gBACvD,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;gBACrD,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;aACrD,CAAA;YAED,gEAAgE;YAChE,8DAA8D;YAC9D,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAA;YACnE,UAAU,CAAC,QAAQ,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;YAExE,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC,CAAA;YAEzE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAC5B,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,SAAS,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAA;QAChD,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,iDAAiD,EAAE,KAAK;YAC1D,MAAM,SAAS,GAAgE;gBAC9E,EAAE,IAAI,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,WAAW,EAAE;gBACvD,EAAE,IAAI,EAAE,sBAAsB,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,cAAc,EAAE;gBACrE,EAAE,IAAI,EAAE,UAAU,EAAE,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE;aACrD,CAAA;YAED,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,wBAAwB,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAA;YACtE,MAAM,OAAO,GAAG;gBACf,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAAC;aAC9D,CAAA;YACD,mCAAmC;YACnC,MAAM,aAAa,GAAG;gBACrB,GAAG,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC;gBAClC,WAAW,EAAE,KAAK,CAAC,IAAI,EAAE;aACzB,CAAA;YAED,uDAAuD;YACvD,8FAA8F;YAC9F,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,sBAAsB,CAAC,CAAC,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,aAAa,EAAE,KAAK,EAAE,EAAE;gBAChG,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,EAAE,CAAC;oBACnB,OAAO,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAA;gBACjC,CAAC;gBAED,qCAAqC;gBACrC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAA;YACtB,CAAC,CAAC,CAAA;YAEF,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,oBAAoB,CAAC,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC,CAAA;YAE5E,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAC5B,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;YAC7B,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;gBACjC,IAAI,EAAE,sBAAsB;gBAC5B,IAAI,EAAE,MAAM;gBACZ,KAAK,EAAE,cAAc;aACrB,CAAC,CAAA;QACH,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,mBAAmB,EAAE;QAC7B,EAAE,CAAC,sEAAsE,EAAE;YAC1E,MAAM,SAAS,GAAuB,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;YAChH,MAAM,SAAS,GAAuB,EAAE,IAAI,EAAE,EAAE,EAAE,SAAS,EAAE,IAAI,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAA;YAEhH,MAAM,MAAM,GAAG,UAAU,CAAC,iBAAiB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAA;YAEjE,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAA;QAC9B,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}