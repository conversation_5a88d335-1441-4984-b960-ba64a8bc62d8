// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
import { createServiceRegistry } from "../grpc-service"
import { registerAllMethods } from "./methods"
// Create state service registry
const stateService = createServiceRegistry("state")
export const registerMethod = stateService.registerMethod
// Export the request handlers
export const handleStateServiceRequest = stateService.handleRequest
export const handleStateServiceStreamingRequest = stateService.handleStreamingRequest
export const isStreamingMethod = stateService.isStreamingMethod
// Register all state methods
registerAllMethods()
//# sourceMappingURL=index.js.map
