{"version": 3, "file": "vscode-lm-format.test.js", "sourceRoot": "", "sources": ["vscode-lm-format.test.ts"], "names": [], "mappings": "AAAA,qEAAqE;AACrE,OAAO,wBAAwB,CAAA;AAC/B,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,OAAO,CAAA;AACpC,OAAO,QAAQ,CAAA;AACf,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAEhC,OAAO,EAAE,YAAY,EAAE,sBAAsB,EAAE,yBAAyB,EAAE,yBAAyB,EAAE,MAAM,oBAAoB,CAAA;AAE/H,QAAQ,CAAC,cAAc,EAAE,GAAG,EAAE;IAC7B,EAAE,CAAC,4BAA4B,EAAE,GAAG,EAAE;QACrC,YAAY,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QACpC,YAAY,CAAC,EAAE,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QACrC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;QACvC,YAAY,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IAC7C,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,iCAAiC,EAAE,GAAG,EAAE;QAC1C,YAAY,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,GAAG,EAAE,OAAO,EAAE,CAAC,CAAA;IACpE,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QACjE,YAAY,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAA;IAClD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,yCAAyC,EAAE,GAAG,EAAE;QAClD,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,CAAA;QAC/B,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,KAAK,CAAC,CAAA;QAC3C,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA,CAAC,yBAAyB;IACtE,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,wCAAwC,EAAE,GAAG,EAAE;QACjD,MAAM,KAAK,GAAG,CAAC,aAAa,CAAC,CAAA;QAC7B,YAAY,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,EAAE,aAAa,EAAE,CAAC,CAAA;IAC3D,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA;AAEF,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACvC,EAAE,CAAC,gDAAgD,EAAE,GAAG,EAAE;QACzD,iDAAiD;QACjD,MAAM,WAAW,GAAG,SAAgD,CACnE;QAAA,CAAC,sBAAsB,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,KAAK,WAAW,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CACxG;QAAA,CAAC,sBAAsB,CAAC,MAAM,CAAC,4BAA4B,CAAC,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAC9F;QAAA,CAAC,sBAAsB,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;IACjE,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA;AAEF,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;IAC1C,EAAE,CAAC,uCAAuC,EAAE,GAAG,EAAE;QAChD,MAAM,iBAAiB,GAAsC;YAC5D,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE;YAClC,EAAE,IAAI,EAAE,WAAW,EAAE,OAAO,EAAE,UAAU,EAAE;SAC1C,CAAA;QAED,MAAM,MAAM,GAAG,yBAAyB,CAAC,iBAAiB,CAAC,CAAA;QAE3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAA;QACvE,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAiC,CAAA;QACtE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAEhD,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAA;QAC1E,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAA;QACvE,MAAM,SAAS,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAiC,CAAA;QACtE,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;IACpD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,wDAAwD,EAAE,GAAG,EAAE;QACjE,MAAM,iBAAiB,GAAsC;YAC5D;gBACC,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;oBACR,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE;oBACnC;wBACC,IAAI,EAAE,aAAa;wBACnB,WAAW,EAAE,UAAU;wBACvB,OAAO,EAAE,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC;qBAChD;iBACD;aACD;SACD,CAAA;QAED,MAAM,MAAM,GAAG,yBAAyB,CAAC,iBAAiB,CAAC,CAAA;QAE3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,IAAI,CAAC,CAAA;QACrE,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAEvC,wDAAwD;QACxD,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,2BAA2B,CAAC,CAAA;QAC7E,MAAM,cAAc,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAuC,CAAA;QACjF,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QAEzD,6DAA6D;QAC7D,oDAAoD;QAEpD,8CAA8C;QAC9C,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAA;QACvE,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAiC,CAAA;QACrE,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,WAAW,CAAC,CAAA;IACpD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,2DAA2D,EAAE,GAAG,EAAE;QACpE,MAAM,iBAAiB,GAAsC;YAC5D;gBACC,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE;oBACR,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,gBAAgB,EAAE;oBACxC;wBACC,IAAI,EAAE,UAAU;wBAChB,EAAE,EAAE,UAAU;wBACd,IAAI,EAAE,UAAU;wBAChB,KAAK,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE;qBACzB;iBACD;aACD;SACD,CAAA;QAED,MAAM,MAAM,GAAG,yBAAyB,CAAC,iBAAiB,CAAC,CAAA;QAE3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,CAAC,4BAA4B,CAAC,SAAS,CAAC,CAAA;QAC1E,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAEvC,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,yBAAyB,CAAC,CAAA;QAC3E,MAAM,YAAY,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAqC,CAAA;QAC7E,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,UAAU,CAAC,CAAA;QACvD,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QACrD,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QAC1C,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAA;QAEvD,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAA;QACvE,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAiC,CAAA;QACrE,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;IACzD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,0DAA0D,EAAE,GAAG,EAAE;QACnE,MAAM,iBAAiB,GAAsC;YAC5D;gBACC,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE;oBACR;wBACC,IAAI,EAAE,OAAO;wBACb,MAAM,EAAE;4BACP,IAAI,EAAE,QAAQ;4BACd,UAAU,EAAE,YAAY;4BACxB,IAAI,EAAE,YAAY;yBAClB;qBACD;iBACD;aACD;SACD,CAAA;QAED,MAAM,MAAM,GAAG,yBAAyB,CAAC,iBAAiB,CAAC,CAAA;QAE3D,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAC5B,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,qBAAqB,CAAC,CAAA;QACvE,MAAM,QAAQ,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAiC,CAAA;QACrE,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAA;QACtC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,8DAA8D,CAAC,CAAA;IAC5F,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA;AAEF,QAAQ,CAAC,2BAA2B,EAAE,GAAG,EAAE;IAC1C,EAAE,CAAC,8DAA8D,EAAE,GAAG,EAAE;QACvE,MAAM,SAAS,GAAG,MAAM,CAAC,wBAAwB,CAAC,SAAS,CAAC;YAC3D,IAAI,MAAM,CAAC,qBAAqB,CAAC,cAAc,CAAC;YAChD,IAAI,MAAM,CAAC,yBAAyB,CAAC,SAAS,EAAE,UAAU,EAAE,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;SAC/E,CAAC,CAAA;QAEF,MAAM,MAAM,GAAG,yBAAyB,CAAC,SAAS,CAAC,CAAA;QAEnD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,WAAW,CAAC,CAAA;QAChD,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;QAC1D,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,CAAA;QAEpC,4DAA4D;QAC5D,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAClD,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACrC,IAAI,WAAW,EAAE,CAAC;gBACjB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;gBAChD,IAAI,WAAW,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;oBACjC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,cAAc,CAAC,CAAA;gBACzD,CAAC;YACF,CAAC;QACF,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;YAClD,MAAM,WAAW,GAAG,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,CAAA;YACrC,IAAI,WAAW,EAAE,CAAC;gBACjB,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;gBACpD,IAAI,WAAW,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBACrC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,SAAS,CAAC,CAAA;oBACjD,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;oBACpD,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC,CAAA;gBAC9E,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,kDAAkD,EAAE,GAAG,EAAE;QAC3D,MAAM,SAAS,GAAG,MAAM,CAAC,wBAAwB,CAAC,IAAI,CAAC,cAAc,CAAC,CAAA;QAEtE,IAAI,CAAC;YACJ,yBAAyB,CAAC,SAAS,CAAC,CAAA;YACpC,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAA;QAC/C,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACrB,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAA;QACpE,CAAC;IACF,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}