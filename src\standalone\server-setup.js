// Account Service
import { account<PERSON>oginClicked } from "../core/controller/account/accountLoginClicked";
import { accountLogoutClicked } from "../core/controller/account/accountLogoutClicked";
// Browser Service
import { getBrowserConnectionInfo } from "../core/controller/browser/getBrowserConnectionInfo";
import { testBrowserConnection } from "../core/controller/browser/testBrowserConnection";
import { discoverBrowser } from "../core/controller/browser/discoverBrowser";
import { getDetectedChromePath } from "../core/controller/browser/getDetectedChromePath";
import { updateBrowserSettings } from "../core/controller/browser/updateBrowserSettings";
import { relaunchChromeDebugMode } from "../core/controller/browser/relaunchChromeDebugMode";
// Checkpoints Service
import { checkpointDiff } from "../core/controller/checkpoints/checkpointDiff";
import { checkpointRestore } from "../core/controller/checkpoints/checkpointRestore";
// File Service
import { copyToClipboard } from "../core/controller/file/copyToClipboard";
import { openFile } from "../core/controller/file/openFile";
import { openImage } from "../core/controller/file/openImage";
import { openMention } from "../core/controller/file/openMention";
import { deleteRuleFile } from "../core/controller/file/deleteRuleFile";
import { createRuleFile } from "../core/controller/file/createRuleFile";
import { searchCommits } from "../core/controller/file/searchCommits";
import { selectImages } from "../core/controller/file/selectImages";
import { getRelativePaths } from "../core/controller/file/getRelativePaths";
import { searchFiles } from "../core/controller/file/searchFiles";
import { toggleClineRule } from "../core/controller/file/toggleClineRule";
import { toggleCursorRule } from "../core/controller/file/toggleCursorRule";
import { toggleWindsurfRule } from "../core/controller/file/toggleWindsurfRule";
import { refreshRules } from "../core/controller/file/refreshRules";
// Mcp Service
import { toggleMcpServer } from "../core/controller/mcp/toggleMcpServer";
import { updateMcpTimeout } from "../core/controller/mcp/updateMcpTimeout";
import { addRemoteMcpServer } from "../core/controller/mcp/addRemoteMcpServer";
import { downloadMcp } from "../core/controller/mcp/downloadMcp";
import { restartMcpServer } from "../core/controller/mcp/restartMcpServer";
import { deleteMcpServer } from "../core/controller/mcp/deleteMcpServer";
import { toggleToolAutoApprove } from "../core/controller/mcp/toggleToolAutoApprove";
import { refreshMcpMarketplace } from "../core/controller/mcp/refreshMcpMarketplace";
// Models Service
import { getOllamaModels } from "../core/controller/models/getOllamaModels";
import { getLmStudioModels } from "../core/controller/models/getLmStudioModels";
import { getVsCodeLmModels } from "../core/controller/models/getVsCodeLmModels";
import { refreshOpenRouterModels } from "../core/controller/models/refreshOpenRouterModels";
import { refreshOpenAiModels } from "../core/controller/models/refreshOpenAiModels";
import { refreshRequestyModels } from "../core/controller/models/refreshRequestyModels";
// Slash Service
import { reportBug } from "../core/controller/slash/reportBug";
import { condense } from "../core/controller/slash/condense";
// State Service
import { getLatestState } from "../core/controller/state/getLatestState";
import { subscribeToState } from "../core/controller/state/subscribeToState";
import { toggleFavoriteModel } from "../core/controller/state/toggleFavoriteModel";
import { resetState } from "../core/controller/state/resetState";
import { togglePlanActMode } from "../core/controller/state/togglePlanActMode";
import { updateTerminalConnectionTimeout } from "../core/controller/state/updateTerminalConnectionTimeout";
// Task Service
import { cancelTask } from "../core/controller/task/cancelTask";
import { clearTask } from "../core/controller/task/clearTask";
import { deleteTasksWithIds } from "../core/controller/task/deleteTasksWithIds";
import { newTask } from "../core/controller/task/newTask";
import { showTaskWithId } from "../core/controller/task/showTaskWithId";
import { exportTaskWithId } from "../core/controller/task/exportTaskWithId";
import { toggleTaskFavorite } from "../core/controller/task/toggleTaskFavorite";
import { deleteNonFavoritedTasks } from "../core/controller/task/deleteNonFavoritedTasks";
import { getTaskHistory } from "../core/controller/task/getTaskHistory";
import { askResponse } from "../core/controller/task/askResponse";
import { taskFeedback } from "../core/controller/task/taskFeedback";
import { taskCompletionViewChanges } from "../core/controller/task/taskCompletionViewChanges";
// Ui Service
import { scrollToSettings } from "../core/controller/ui/scrollToSettings";
// Web Service
import { checkIsImageUrl } from "../core/controller/web/checkIsImageUrl";
import { fetchOpenGraphData } from "../core/controller/web/fetchOpenGraphData";
export function addServices(server, proto, controller, wrapper, wrapStreamingResponse) {
    // Account Service
    server.addService(proto.cline.AccountService.service, {
        accountLoginClicked: wrapper(accountLoginClicked, controller),
        accountLogoutClicked: wrapper(accountLogoutClicked, controller),
    });
    // Browser Service
    server.addService(proto.cline.BrowserService.service, {
        getBrowserConnectionInfo: wrapper(getBrowserConnectionInfo, controller),
        testBrowserConnection: wrapper(testBrowserConnection, controller),
        discoverBrowser: wrapper(discoverBrowser, controller),
        getDetectedChromePath: wrapper(getDetectedChromePath, controller),
        updateBrowserSettings: wrapper(updateBrowserSettings, controller),
        relaunchChromeDebugMode: wrapper(relaunchChromeDebugMode, controller),
    });
    // Checkpoints Service
    server.addService(proto.cline.CheckpointsService.service, {
        checkpointDiff: wrapper(checkpointDiff, controller),
        checkpointRestore: wrapper(checkpointRestore, controller),
    });
    // File Service
    server.addService(proto.cline.FileService.service, {
        copyToClipboard: wrapper(copyToClipboard, controller),
        openFile: wrapper(openFile, controller),
        openImage: wrapper(openImage, controller),
        openMention: wrapper(openMention, controller),
        deleteRuleFile: wrapper(deleteRuleFile, controller),
        createRuleFile: wrapper(createRuleFile, controller),
        searchCommits: wrapper(searchCommits, controller),
        selectImages: wrapper(selectImages, controller),
        getRelativePaths: wrapper(getRelativePaths, controller),
        searchFiles: wrapper(searchFiles, controller),
        toggleClineRule: wrapper(toggleClineRule, controller),
        toggleCursorRule: wrapper(toggleCursorRule, controller),
        toggleWindsurfRule: wrapper(toggleWindsurfRule, controller),
        refreshRules: wrapper(refreshRules, controller),
    });
    // Mcp Service
    server.addService(proto.cline.McpService.service, {
        toggleMcpServer: wrapper(toggleMcpServer, controller),
        updateMcpTimeout: wrapper(updateMcpTimeout, controller),
        addRemoteMcpServer: wrapper(addRemoteMcpServer, controller),
        downloadMcp: wrapper(downloadMcp, controller),
        restartMcpServer: wrapper(restartMcpServer, controller),
        deleteMcpServer: wrapper(deleteMcpServer, controller),
        toggleToolAutoApprove: wrapper(toggleToolAutoApprove, controller),
        refreshMcpMarketplace: wrapper(refreshMcpMarketplace, controller),
    });
    // Models Service
    server.addService(proto.cline.ModelsService.service, {
        getOllamaModels: wrapper(getOllamaModels, controller),
        getLmStudioModels: wrapper(getLmStudioModels, controller),
        getVsCodeLmModels: wrapper(getVsCodeLmModels, controller),
        refreshOpenRouterModels: wrapper(refreshOpenRouterModels, controller),
        refreshOpenAiModels: wrapper(refreshOpenAiModels, controller),
        refreshRequestyModels: wrapper(refreshRequestyModels, controller),
    });
    // Slash Service
    server.addService(proto.cline.SlashService.service, {
        reportBug: wrapper(reportBug, controller),
        condense: wrapper(condense, controller),
    });
    // State Service
    server.addService(proto.cline.StateService.service, {
        getLatestState: wrapper(getLatestState, controller),
        subscribeToState: wrapStreamingResponse(subscribeToState, controller),
        toggleFavoriteModel: wrapper(toggleFavoriteModel, controller),
        resetState: wrapper(resetState, controller),
        togglePlanActMode: wrapper(togglePlanActMode, controller),
        updateTerminalConnectionTimeout: wrapper(updateTerminalConnectionTimeout, controller),
    });
    // Task Service
    server.addService(proto.cline.TaskService.service, {
        cancelTask: wrapper(cancelTask, controller),
        clearTask: wrapper(clearTask, controller),
        deleteTasksWithIds: wrapper(deleteTasksWithIds, controller),
        newTask: wrapper(newTask, controller),
        showTaskWithId: wrapper(showTaskWithId, controller),
        exportTaskWithId: wrapper(exportTaskWithId, controller),
        toggleTaskFavorite: wrapper(toggleTaskFavorite, controller),
        deleteNonFavoritedTasks: wrapper(deleteNonFavoritedTasks, controller),
        getTaskHistory: wrapper(getTaskHistory, controller),
        askResponse: wrapper(askResponse, controller),
        taskFeedback: wrapper(taskFeedback, controller),
        taskCompletionViewChanges: wrapper(taskCompletionViewChanges, controller),
    });
    // Ui Service
    server.addService(proto.cline.UiService.service, {
        scrollToSettings: wrapper(scrollToSettings, controller),
    });
    // Web Service
    server.addService(proto.cline.WebService.service, {
        checkIsImageUrl: wrapper(checkIsImageUrl, controller),
        fetchOpenGraphData: wrapper(fetchOpenGraphData, controller),
    });
}
//# sourceMappingURL=server-setup.js.map