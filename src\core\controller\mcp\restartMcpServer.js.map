{"version": 3, "file": "restartMcpServer.js", "sourceRoot": "", "sources": ["restartMcpServer.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,kCAAkC,EAAE,MAAM,qDAAqD,CAAA;AAGxG;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,UAAsB,EAAE,OAAsB;IACpF,IAAI,CAAC;QACJ,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE,oBAAoB,CAAC,OAAO,CAAC,KAAK,CAAC,CAAA;QAE/E,oFAAoF;QACpF,MAAM,YAAY,GAAG,kCAAkC,CAAC,UAAU,CAAC,CAAA;QAEnE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAA;IACpC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,gCAAgC,OAAO,CAAC,KAAK,GAAG,EAAE,KAAK,CAAC,CAAA;QACtE,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}