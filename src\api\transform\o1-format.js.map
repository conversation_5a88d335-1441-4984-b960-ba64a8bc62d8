{"version": 3, "file": "o1-format.js", "sourceRoot": "", "sources": ["o1-format.ts"], "names": [], "mappings": "AAGA,MAAM,cAAc,GAAG,CAAC,YAAoB,EAAE,EAAE,CAAC;;;EAG/C,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA+Jb,CAAA;AAED,MAAM,UAAU,mBAAmB,CAClC,cAAwD,EACxD,YAAoB;IAEpB,MAAM,aAAa,GAAG,cAAc,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;QAC5D,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;YAC7B,yCAAyC;YACzC,GAAG,CAAC,IAAI,CAAC;gBACR,IAAI,EAAE,MAAM;gBACZ,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;aAC9B,CAAC,CAAA;QACH,CAAC;aAAM,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YAC/D,sDAAsD;YACtD,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,IAAI,EAAE,CAAA;YACnC,OAAO,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,QAAQ,EAAE,EAAE;gBACvC,IAAI,QAAQ,CAAC,IAAI,KAAK,UAAU,EAAE,CAAC;oBAClC,OAAO,IAAI,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,IAAI,gBAAgB,QAAQ,CAAC,QAAQ,CAAC,SAAS,EAAE,CAAA;gBAC/F,CAAC;YACF,CAAC,CAAC,CAAA;YACF,GAAG,CAAC,IAAI,CAAC;gBACR,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,SAAS;aACrB,CAAC,CAAA;QACH,CAAC;aAAM,CAAC;YACP,kCAAkC;YAClC,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;QAClB,CAAC;QACD,OAAO,GAAG,CAAA;IACX,CAAC,EAAE,EAA8C,CAAC,CAAA;IAElD,+CAA+C;IAC/C,qGAAqG;IAErG,mDAAmD;IACnD,MAAM,wBAAwB,GAAG;QAChC;YACC,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,cAAc,CAAC,YAAY,CAAC;SACS;QAC/C,GAAG,aAAa;KAChB,CAAA;IAED,qEAAqE;IACrE,mCAAmC;IACnC,8CAA8C;IAC9C,iHAAiH;IACjH,sDAAsD;IACtD,mBAAmB;IACnB,4CAA4C;IAC5C,OAAO;IACP,KAAK;IACL,WAAW;IACX,qEAAqE;IACrE,sCAAsC;IACtC,kBAAkB;IAClB,2CAA2C;IAC3C,MAAM;IACN,IAAI;IAEJ,OAAO,wBAAwB,CAAA;AAChC,CAAC;AAOD,MAAM,SAAS,GAAG;IACjB,iBAAiB;IACjB,YAAY;IACZ,4BAA4B;IAC5B,cAAc;IACd,WAAW;IACX,eAAe;IACf,uBAAuB;IACvB,oBAAoB;CACpB,CAAA;AAED,SAAS,eAAe,CAAC,QAAgB;IAIxC,4DAA4D;IAC5D,MAAM,eAAe,GAAG,IAAI,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;IACpE,MAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;IAE7C,IAAI,CAAC,KAAK,EAAE,CAAC;QACZ,sBAAsB;QACtB,OAAO,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,CAAA;IACtD,CAAC;IAED,MAAM,aAAa,GAAG,KAAK,CAAC,KAAM,CAAA;IAClC,MAAM,UAAU,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,IAAI,EAAE,CAAA;IAC1D,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;IAEnD,MAAM,SAAS,GAAG,cAAc,CAAC,aAAa,CAAC,CAAA;IAE/C,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,CAAA;AACjC,CAAC;AAED,SAAS,cAAc,CAAC,aAAqB;IAC5C,MAAM,SAAS,GAAe,EAAE,CAAA;IAEhC,IAAI,aAAa,GAAG,aAAa,CAAA;IAEjC,OAAO,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACjC,MAAM,SAAS,GAAG,SAAS,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,MAAM,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC,CAAA;QAE3F,IAAI,CAAC,SAAS,EAAE,CAAC;YAChB,MAAK,CAAC,2BAA2B;QAClC,CAAC;QAED,MAAM,QAAQ,GAAG,IAAI,SAAS,EAAE,CAAA;QAChC,MAAM,MAAM,GAAG,KAAK,SAAS,GAAG,CAAA;QAChC,MAAM,UAAU,GAAG,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;QAClD,MAAM,QAAQ,GAAG,aAAa,CAAC,OAAO,CAAC,MAAM,EAAE,UAAU,CAAC,CAAA;QAE1D,IAAI,QAAQ,KAAK,CAAC,CAAC,EAAE,CAAC;YACrB,MAAK,CAAC,sCAAsC;QAC7C,CAAC;QAED,MAAM,eAAe,GAAG,aAAa,CAAC,KAAK,CAAC,UAAU,EAAE,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAA;QACjF,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,QAAQ,GAAG,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI,EAAE,CAAA;QAEpE,MAAM,QAAQ,GAAG,aAAa,CAAC,SAAS,EAAE,eAAe,CAAC,CAAA;QAC1D,IAAI,QAAQ,EAAE,CAAC;YACd,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QACzB,CAAC;IACF,CAAC;IAED,OAAO,SAAS,CAAA;AACjB,CAAC;AAED,SAAS,aAAa,CAAC,QAAgB,EAAE,OAAe;IACvD,MAAM,UAAU,GAA2B,EAAE,CAAA;IAE7C,6BAA6B;IAC7B,MAAM,YAAY,GAAG,OAAO,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,KAAK,QAAQ,OAAO,QAAQ,IAAI,EAAE,GAAG,CAAC,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;IAElG,4BAA4B;IAC5B,MAAM,UAAU,GAAG,2BAA2B,CAAA;IAC9C,IAAI,KAA6B,CAAA;IAEjC,OAAO,CAAC,KAAK,GAAG,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,KAAK,IAAI,EAAE,CAAC;QACzD,MAAM,CAAC,EAAE,SAAS,EAAE,UAAU,CAAC,GAAG,KAAK,CAAA;QACvC,8DAA8D;QAC9D,UAAU,CAAC,SAAS,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,YAAY,EAAE,EAAE,CAAC,CAAA;IAC7D,CAAC;IAED,+BAA+B;IAC/B,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,CAAC;QAC9C,OAAO,CAAC,KAAK,CAAC,yBAAyB,QAAQ,GAAG,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,IAAI,CAAA;IACZ,CAAC;IAED,OAAO,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAA;AACtC,CAAC;AAED,SAAS,iBAAiB,CAAC,QAAgB,EAAE,UAAkC;IAC9E,QAAQ,QAAQ,EAAE,CAAC;QAClB,KAAK,iBAAiB;YACrB,OAAO,SAAS,IAAI,UAAU,CAAA;QAC/B,KAAK,WAAW,CAAC;QACjB,KAAK,4BAA4B,CAAC;QAClC,KAAK,YAAY;YAChB,OAAO,MAAM,IAAI,UAAU,CAAA;QAC5B,KAAK,cAAc;YAClB,OAAO,MAAM,IAAI,UAAU,IAAI,OAAO,IAAI,UAAU,CAAA;QACrD,KAAK,eAAe;YACnB,OAAO,MAAM,IAAI,UAAU,IAAI,SAAS,IAAI,UAAU,CAAA;QACvD,KAAK,uBAAuB;YAC3B,OAAO,UAAU,IAAI,UAAU,CAAA;QAChC,KAAK,oBAAoB;YACxB,OAAO,QAAQ,IAAI,UAAU,CAAA;QAC9B;YACC,OAAO,KAAK,CAAA;IACd,CAAC;AACF,CAAC;AAED,iBAAiB;AACjB,6DAA6D;AAE7D,oBAAoB;AACpB,8BAA8B;AAC9B,qBAAqB;AAErB,kBAAkB;AAClB,+BAA+B;AAC/B,qCAAqC;AACrC,qBAAqB;AACrB,EAAE;AACF,iEAAiE;AACjE,2BAA2B;AAC3B,0BAA0B;AAE1B,8CAA8C;AAC9C,MAAM,UAAU,mCAAmC,CAClD,UAAkD;IAElD,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,OAAO,CAAA;IACnD,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,eAAe,CAAC,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC,CAAA;IAE9E,MAAM,gBAAgB,GAA+B;QACpD,EAAE,EAAE,UAAU,CAAC,EAAE;QACjB,IAAI,EAAE,SAAS;QACf,IAAI,EAAE,aAAa,CAAC,IAAI,EAAE,qBAAqB;QAC/C,OAAO,EAAE;YACR;gBACC,IAAI,EAAE,MAAM;gBACZ,IAAI,EAAE,UAAU;gBAChB,SAAS,EAAE,IAAI;aACf;SACD;QACD,KAAK,EAAE,UAAU,CAAC,KAAK;QACvB,WAAW,EAAE,CAAC,GAAG,EAAE;YAClB,QAAQ,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,aAAa,EAAE,CAAC;gBAC7C,KAAK,MAAM;oBACV,OAAO,UAAU,CAAA;gBAClB,KAAK,QAAQ;oBACZ,OAAO,YAAY,CAAA;gBACpB,KAAK,YAAY;oBAChB,OAAO,UAAU,CAAA;gBAClB,KAAK,gBAAgB,CAAC,CAAC,6CAA6C;gBACpE;oBACC,OAAO,IAAI,CAAA;YACb,CAAC;QACF,CAAC,CAAC,EAAE;QACJ,aAAa,EAAE,IAAI,EAAE,mGAAmG;QACxH,KAAK,EAAE;YACN,YAAY,EAAE,UAAU,CAAC,KAAK,EAAE,aAAa,IAAI,CAAC;YAClD,aAAa,EAAE,UAAU,CAAC,KAAK,EAAE,iBAAiB,IAAI,CAAC;YACvD,2BAA2B,EAAE,IAAI;YACjC,uBAAuB,EAAE,IAAI;SAC7B;KACD,CAAA;IAED,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QAC1B,gBAAgB,CAAC,OAAO,CAAC,IAAI,CAC5B,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,QAAkB,EAAE,KAAa,EAA0B,EAAE;YAC9E,OAAO;gBACN,IAAI,EAAE,UAAU;gBAChB,EAAE,EAAE,QAAQ,KAAK,IAAI,IAAI,CAAC,GAAG,EAAE,EAAE,EAAE,0CAA0C;gBAC7E,IAAI,EAAE,QAAQ,CAAC,IAAI;gBACnB,KAAK,EAAE,QAAQ,CAAC,UAAU;aAC1B,CAAA;QACF,CAAC,CAAC,CACF,CAAA;IACF,CAAC;IAED,OAAO,gBAAgB,CAAA;AACxB,CAAC;AAED,iBAAiB;AACjB,6BAA6B;AAC7B,sBAAsB;AACtB,kBAAkB;AAClB,qBAAqB;AACrB,iCAAiC;AACjC,qHAAqH;AACrH,aAAa;AACb,gCAAgC;AAChC,UAAU;AACV,8BAA8B;AAC9B,2DAA2D;AAC3D,KAAK;AACL,kFAAkF;AAClF,iCAAiC"}