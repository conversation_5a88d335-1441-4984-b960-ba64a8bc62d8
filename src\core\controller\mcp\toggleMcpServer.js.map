{"version": 3, "file": "toggleMcpServer.js", "sourceRoot": "", "sources": ["toggleMcpServer.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,kCAAkC,EAAE,MAAM,6DAA6D,CAAA;AAEhH;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CAAC,UAAsB,EAAE,OAA+B;IAC5F,IAAI,CAAC;QACJ,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE,uBAAuB,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,QAAQ,CAAC,CAAA;QAEzG,oFAAoF;QACpF,MAAM,YAAY,GAAG,kCAAkC,CAAC,UAAU,CAAC,CAAA;QAEnE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,CAAA;IACpC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,+BAA+B,OAAO,CAAC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAA;QAC1E,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}