// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
import { createServiceRegistry } from "../grpc-service"
import { registerAllMethods } from "./methods"
// Create web service registry
const webService = createServiceRegistry("web")
export const registerMethod = webService.registerMethod
// Export the request handlers
export const handleWebServiceRequest = webService.handleRequest
export const handleWebServiceStreamingRequest = webService.handleStreamingRequest
export const isStreamingMethod = webService.isStreamingMethod
// Register all web methods
registerAllMethods()
//# sourceMappingURL=index.js.map
