import * as vscode from "vscode"

export interface TerminalInfo {
	// 基础信息 (Basic Information)
	terminal: vscode.Terminal
	busy: boolean
	lastCommand: string
	id: number

	// 终端类型信息 (Terminal Type Information)
	terminalType?: 'local' | 'remote'    // 终端类型 (Terminal Type)
	shellType?: 'powershell' | 'bash' | 'cmd' | 'zsh' | 'other'  // shell类型 (Shell Type)

	// 远程信息 (Remote Information)
	remoteAddress?: string              // 远程IP/主机地址 (Remote IP/Host Address)
	deviceType?: 'linux' | 'network' | 'other'  // 设备类型 (Device Type)

	// 会话状态 (Session State)
	isLongRunningSession?: boolean      // 是否为长期会话 (Is Long Running Session)
	sessionType?: 'standard' | 'interactive' | 'long_running'  // 会话类型 (Session Type)
	lastPrompt?: string                 // 最近检测到的提示符 (Last Detected Prompt)

	// 内部状态跟踪 (Internal State Tracking)
	pendingCwdChange?: string
	cwdResolved?: {
		resolve: () => void
		reject: (error: Error) => void
	}

	// 交互失败信息 (Interaction Failure Information)
	lastInteractionFailure?: {
		command: string
		failedStep: number
		stepPrompt?: string
	}

	// 连接失败信息 (Connection Failure Information)
	lastConnectionFailure?: {
		reason: string
		timestamp: number
		output: string
	}
}

// Although vscode.window.terminals provides a list of all open terminals, there's no way to know whether they're busy or not (exitStatus does not provide useful information for most commands). In order to prevent creating too many terminals, we need to keep track of terminals through the life of the extension, as well as session specific terminals for the life of a task (to get latest unretrieved output).
// Since we have promises keeping track of terminal processes, we get the added benefit of keep track of busy terminals even after a task is closed.
export class TerminalRegistry {
	private static terminals: TerminalInfo[] = []
	private static nextTerminalId = 1

	static createTerminal(cwd?: string | vscode.Uri | undefined): TerminalInfo {
		const terminal = vscode.window.createTerminal({
			cwd,
			name: "Cline",
			iconPath: new vscode.ThemeIcon("robot"),
			location: vscode.TerminalLocation.Editor
		})

		// Detect local shell type based on platform
		const shellType = this.detectLocalShellType()

		const newInfo: TerminalInfo = {
			terminal,
			busy: false,
			lastCommand: "",
			id: this.nextTerminalId++,
			terminalType: 'local',  // Default to local terminal
			shellType,
			sessionType: 'standard' // Default to standard session
		}
		this.terminals.push(newInfo)
		return newInfo
	}

	/**
	 * Detects the local shell type based on platform and environment variables
	 * @returns The detected shell type
	 */
	private static detectLocalShellType(): 'powershell' | 'bash' | 'cmd' | 'zsh' | 'other' {
		// Based on platform and environment variables
		if (process.platform === 'win32') {
			// On Windows, check if PowerShell or CMD
			return process.env.SHELL?.toLowerCase().includes('powershell') ? 'powershell' : 'cmd'
		} else {
			// On Unix-like systems, check if zsh or bash
			const shell = process.env.SHELL?.toLowerCase() || ''
			if (shell.includes('zsh')) {
				return 'zsh'
			} else if (shell.includes('bash')) {
				return 'bash'
			} else {
				return 'other'
			}
		}
	}

	static getTerminal(id: number): TerminalInfo | undefined {
		const terminalInfo = this.terminals.find((t) => t.id === id)
		if (terminalInfo && this.isTerminalClosed(terminalInfo.terminal)) {
			this.removeTerminal(id)
			return undefined
		}
		return terminalInfo
	}

	static updateTerminal(id: number, updates: Partial<TerminalInfo>) {
		const terminal = this.getTerminal(id)
		if (terminal) {
			Object.assign(terminal, updates)
		}
	}

	static removeTerminal(id: number) {
		this.terminals = this.terminals.filter((t) => t.id !== id)
	}

	static getAllTerminals(): TerminalInfo[] {
		this.terminals = this.terminals.filter((t) => !this.isTerminalClosed(t.terminal))
		return this.terminals
	}

	// The exit status of the terminal will be undefined while the terminal is active. (This value is set when onDidCloseTerminal is fired.)
	private static isTerminalClosed(terminal: vscode.Terminal): boolean {
		return terminal.exitStatus !== undefined
	}
}
