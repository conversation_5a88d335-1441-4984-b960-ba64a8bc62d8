{"version": 3, "file": "rule-files-conversion.js", "sourceRoot": "", "sources": ["rule-files-conversion.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,eAAe,EAAE,MAAM,kBAAkB,CAAA;AAElD,sCAAsC;AACtC,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACpC,MAAM,EAAE,CAAC,MAA8E,EAAmB,EAAE;QAC3G,OAAO,eAAe,CAAC,MAAM,CAAC;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,IAAI,EAAE,MAAM,CAAC,IAAI;SACjB,CAAC,CAAA;IACH,CAAC;CACD,CAAA;AAED,sCAAsC;AACtC,MAAM,CAAC,MAAM,qBAAqB,GAAG;IACpC,MAAM,EAAE,CAAC,MAA8E,EAAmB,EAAE;QAC3G,OAAO,eAAe,CAAC,MAAM,CAAC;YAC7B,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,QAAQ,EAAE,MAAM,CAAC,QAAQ;YACzB,IAAI,EAAE,MAAM,CAAC,IAAI;SACjB,CAAC,CAAA;IACH,CAAC;CACD,CAAA"}