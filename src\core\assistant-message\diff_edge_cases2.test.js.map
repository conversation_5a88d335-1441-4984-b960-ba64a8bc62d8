{"version": 3, "file": "diff_edge_cases2.test.js", "sourceRoot": "", "sources": ["diff_edge_cases2.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,uBAAuB,IAAI,KAAK,EAAE,MAAM,QAAQ,CAAA;AACzD,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,OAAO,CAAA;AACpC,OAAO,EAAE,MAAM,EAAE,MAAM,MAAM,CAAA;AAE7B,KAAK,UAAU,IAAI,CAAC,WAAmB,EAAE,eAAuB,EAAE,OAAgB;IACjF,OAAO,KAAK,CAAC,WAAW,EAAE,eAAe,EAAE,OAAO,EAAE,IAAI,CAAC,CAAA;AAC1D,CAAC;AAED,QAAQ,CAAC,wBAAwB,EAAE,GAAG,EAAE;IACvC,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QACnD,MAAM,QAAQ,GAAG,cAAc,CAAA;QAC/B,MAAM,IAAI,GAAG;;gBAEC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,eAAe,CAAC,CAAA;QACzC,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;QACxD,MAAM,QAAQ,GAAG,MAAM,CAAA;QACvB,MAAM,IAAI,GAAG;;;;;;;gBAOC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,qBAAqB,CAAC,CAAA;QAC/C,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,qCAAqC,EAAE,KAAK,IAAI,EAAE;QACpD,MAAM,QAAQ,GAAG,SAAS,CAAA;QAC1B,MAAM,IAAI,GAAG;;;gBAGC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,kBAAkB,CAAC,CAAA;QAC5C,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;QACzD,MAAM,QAAQ,GAAG,YAAY,CAAA;QAC7B,MAAM,IAAI,GAAG;;gBAEC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA;QAC7B,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QACjD,MAAM,QAAQ,GAAG,aAAa,CAAA;QAC9B,MAAM,IAAI,GAAG;;;gBAGC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QACtC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;QACjD,MAAM,QAAQ,GAAG,gBAAgB,CAAA;QACjC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,cAAc,CAAC,CAAA;QACxC,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;QAC3D,MAAM,QAAQ,GAAG,qBAAqB,CAAA;QACtC,MAAM,IAAI,GAAG;;;;gBAIC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,yBAAyB,CAAC,CAAA;QACnD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,6DAA6D,EAAE,KAAK,IAAI,EAAE;QAC5E,MAAM,QAAQ,GAAG,iCAAiC,CAAA;QAClD,MAAM,IAAI,GAAG;;;;;;;;;;gBAUC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;QAC3D,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,QAAQ,GAAG,iCAAiC,CAAA;QAClD,MAAM,IAAI,GAAG;;;;;;;;;;gBAUC,CAAA;QACd,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YAChC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;QACD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;IAC5D,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,uDAAuD,EAAE,KAAK,IAAI,EAAE;QACtE,MAAM,QAAQ,GAAG,iCAAiC,CAAA;QAClD,MAAM,IAAI,GAAG;;;;;;;;;;gBAUC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QAC9C,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,kDAAkD,EAAE,KAAK,IAAI,EAAE;QACjE,MAAM,QAAQ,GAAG,kCAAkC,CAAA;QACnD,MAAM,IAAI,GAAG;;;;;;;;;;gBAUC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;IAC5D,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,2DAA2D,EAAE,KAAK,IAAI,EAAE;QAC1E,MAAM,QAAQ,GAAG,4CAA4C,CAAA;QAC7D,MAAM,IAAI,GAAG;;;;;;;;;;gBAUC,CAAA;QACd,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QACjD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,iCAAiC,CAAC,CAAA;IAC5D,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,gEAAgE,EAAE,KAAK,IAAI,EAAE;QAC/E,MAAM,QAAQ,GAAG,4CAA4C,CAAA;QAC7D,MAAM,IAAI,GAAG;;;;;;;;;OASR,CAAA;QACL,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;QACjD,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAA;QAClD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QAC9C,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAA;IACzD,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;QACvF,MAAM,QAAQ,GAAG,4CAA4C,CAAA;QAC7D,MAAM,IAAI,GAAG;;;;;;;;;OASR,CAAA;QACL,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAA;QAC9C,IAAI,CAAC;YACJ,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;YACjC,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;QAC9C,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;QAC9B,CAAC;IACF,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,mEAAmE,EAAE,KAAK,IAAI,EAAE;QAClF,MAAM,QAAQ,GAAG;;;;;kCAKe,CAAA;QAEhC,MAAM,IAAI,GAAG;;;;;;;;;;;;;;;;gBAgBC,CAAA;QAEd,MAAM,QAAQ,GAAG;;;;;;CAMlB,CAAA;QAEC,MAAM,MAAM,GAAG,MAAM,KAAK,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;QAChD,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;IAClC,CAAC,CAAC,CAAA;IAEF,0EAA0E;IAC1E,MAAM,IAAI,GAAG;;;;;;;;;;gBAUE,CAAA;IACf,gFAAgF;IAChF,uEAAuE;IACvE,MAAM,SAAS,GAAG;QACjB,EAAE;QACF,EAAE;QACF,EAAE;QACF,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,oBAAoB;KACpB,CAAA;IACD,oEAAoE;IACpE,uEAAuE;IACvE,MAAM,SAAS,GAAG;QACjB,EAAE;QACF,EAAE;QACF,YAAY;QACZ,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,IAAI,KAAK,EAAE;QACX,IAAI,KAAK,EAAE;KACX,CAAA;IACD,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,EAAE,CAAC,wDAAwD,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,QAAQ,GAAG,iCAAiC,CAAA;YAClD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;YAClG,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAA;QAC3C,CAAC,CAAC,CAAA;IACH,CAAC;IAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;QAC3C,EAAE,CAAC,wDAAwD,CAAC,GAAG,EAAE,KAAK,IAAI,EAAE;YAC3E,MAAM,QAAQ,GAAG,iCAAiC,CAAA;YAClD,IAAI,QAAQ,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAA;YAC/B,IAAI,QAAQ,YAAY,KAAK,EAAE,CAAC;gBAC/B,IAAI,CAAC;oBACJ,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,IAAI,CAAC,CAAA;oBAC7D,MAAM,CAAC,IAAI,CAAC,gCAAgC,CAAC,CAAA;gBAC9C,CAAC;gBAAC,OAAO,GAAG,EAAE,CAAC;oBACd,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO,CAAC,CAAA;gBAC9B,CAAC;YACF,CAAC;iBAAM,CAAC;gBACP,MAAM,OAAO,GAAG,MAAM,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,KAAK,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,CAAA;gBACnG,MAAM,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YACnC,CAAC;QACF,CAAC,CAAC,CAAA;IACH,CAAC;AACF,CAAC,CAAC,CAAA"}