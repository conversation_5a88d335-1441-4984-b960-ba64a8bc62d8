{"version": 3, "file": "grpc-service-config.js", "sourceRoot": "", "sources": ["grpc-service-config.ts"], "names": [], "mappings": "AAAA,+CAA+C;AAC/C,oCAAoC;AAIpC,OAAO,EAAE,2BAA2B,EAAE,oCAAoC,EAAE,MAAM,iBAAiB,CAAA;AACnG,OAAO,EAAE,2BAA2B,EAAE,oCAAoC,EAAE,MAAM,iBAAiB,CAAA;AACnG,OAAO,EAAE,+BAA+B,EAAE,wCAAwC,EAAE,MAAM,qBAAqB,CAAA;AAC/G,OAAO,EAAE,wBAAwB,EAAE,iCAAiC,EAAE,MAAM,cAAc,CAAA;AAC1F,OAAO,EAAE,uBAAuB,EAAE,gCAAgC,EAAE,MAAM,aAAa,CAAA;AACvF,OAAO,EAAE,yBAAyB,EAAE,kCAAkC,EAAE,MAAM,eAAe,CAAA;AAC7F,OAAO,EAAE,wBAAwB,EAAE,iCAAiC,EAAE,MAAM,cAAc,CAAA;AAC1F,OAAO,EAAE,uBAAuB,EAAE,gCAAgC,EAAE,MAAM,aAAa,CAAA;AACvF,OAAO,EAAE,0BAA0B,EAAE,mCAAmC,EAAE,MAAM,gBAAgB,CAAA;AAChG,OAAO,EAAE,yBAAyB,EAAE,kCAAkC,EAAE,MAAM,eAAe,CAAA;AAC7F,OAAO,EAAE,sBAAsB,EAAE,+BAA+B,EAAE,MAAM,YAAY,CAAA;AAgBpF;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAyC;IACpE,sBAAsB,EAAE;QACvB,cAAc,EAAE,2BAA2B;QAC3C,gBAAgB,EAAE,oCAAoC;KACtD;IACD,sBAAsB,EAAE;QACvB,cAAc,EAAE,2BAA2B;QAC3C,gBAAgB,EAAE,oCAAoC;KACtD;IACD,0BAA0B,EAAE;QAC3B,cAAc,EAAE,+BAA+B;QAC/C,gBAAgB,EAAE,wCAAwC;KAC1D;IACD,mBAAmB,EAAE;QACpB,cAAc,EAAE,wBAAwB;QACxC,gBAAgB,EAAE,iCAAiC;KACnD;IACD,kBAAkB,EAAE;QACnB,cAAc,EAAE,uBAAuB;QACvC,gBAAgB,EAAE,gCAAgC;KAClD;IACD,oBAAoB,EAAE;QACrB,cAAc,EAAE,yBAAyB;QACzC,gBAAgB,EAAE,kCAAkC;KACpD;IACD,mBAAmB,EAAE;QACpB,cAAc,EAAE,wBAAwB;QACxC,gBAAgB,EAAE,iCAAiC;KACnD;IACD,kBAAkB,EAAE;QACnB,cAAc,EAAE,uBAAuB;QACvC,gBAAgB,EAAE,gCAAgC;KAClD;IACD,qBAAqB,EAAE;QACtB,cAAc,EAAE,0BAA0B;QAC1C,gBAAgB,EAAE,mCAAmC;KACrD;IACD,oBAAoB,EAAE;QACrB,cAAc,EAAE,yBAAyB;QACzC,gBAAgB,EAAE,kCAAkC;KACpD;IACD,iBAAiB,EAAE;QAClB,cAAc,EAAE,sBAAsB;QACtC,gBAAgB,EAAE,+BAA+B;KACjD;CACD,CAAA"}