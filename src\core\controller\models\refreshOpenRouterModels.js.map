{"version": 3, "file": "refreshOpenRouterModels.js", "sourceRoot": "", "sources": ["refreshOpenRouterModels.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,6BAA6B,EAAuB,MAAM,8BAA8B,CAAA;AACjG,OAAO,KAAK,MAAM,OAAO,CAAA;AACzB,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,MAAM,aAAa,CAAA;AAC5B,OAAO,EAAE,gBAAgB,EAAE,MAAM,WAAW,CAAA;AAC5C,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAA;AAEpD;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAC5C,UAAsB,EACtB,OAAqB;IAErB,MAAM,wBAAwB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,0BAA0B,CAAC,UAAU,CAAC,EAAE,eAAe,CAAC,gBAAgB,CAAC,CAAA;IAE1H,IAAI,MAAM,GAAiD,EAAE,CAAA;IAC7D,IAAI,CAAC;QACJ,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAA;QAEvE,IAAI,QAAQ,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC;YACzB,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAA;YACpC,MAAM,UAAU,GAAG,CAAC,KAAU,EAAE,EAAE;gBACjC,IAAI,KAAK,EAAE,CAAC;oBACX,OAAO,UAAU,CAAC,KAAK,CAAC,GAAG,SAAS,CAAA;gBACrC,CAAC;gBACD,OAAO,SAAS,CAAA;YACjB,CAAC,CAAA;YACD,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE,CAAC;gBAClC,MAAM,SAAS,GAAiC;oBAC/C,SAAS,EAAE,QAAQ,CAAC,YAAY,EAAE,qBAAqB;oBACvD,aAAa,EAAE,QAAQ,CAAC,cAAc;oBACtC,cAAc,EAAE,QAAQ,CAAC,YAAY,EAAE,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC;oBAClE,mBAAmB,EAAE,KAAK;oBAC1B,UAAU,EAAE,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,MAAM,CAAC;oBAChD,WAAW,EAAE,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,UAAU,CAAC;oBACrD,WAAW,EAAE,QAAQ,CAAC,WAAW;iBACjC,CAAA;gBAED,QAAQ,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACrB,KAAK,6BAA6B,CAAC;oBACnC,KAAK,kCAAkC,CAAC;oBACxC,KAAK,6BAA6B,CAAC;oBACnC,KAAK,kCAAkC,CAAC;oBACxC,KAAK,sCAAsC,CAAC;oBAC5C,KAAK,6BAA6B,CAAC;oBACnC,KAAK,kCAAkC;wBACtC,0EAA0E;wBAC1E,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;wBACpC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAA;wBACjC,SAAS,CAAC,eAAe,GAAG,GAAG,CAAA;wBAC/B,MAAK;oBACN,KAAK,sCAAsC,CAAC;oBAC5C,KAAK,2CAA2C;wBAC/C,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;wBACpC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAA;wBACjC,SAAS,CAAC,eAAe,GAAG,GAAG,CAAA;wBAC/B,MAAK;oBACN,KAAK,4BAA4B,CAAC;oBAClC,KAAK,iCAAiC,CAAC;oBACvC,KAAK,qCAAqC,CAAC;oBAC3C,KAAK,0CAA0C,CAAC;oBAChD,KAAK,4BAA4B,CAAC;oBAClC,KAAK,iCAAiC,CAAC;oBACvC,KAAK,qCAAqC,CAAC;oBAC3C,KAAK,0CAA0C;wBAC9C,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;wBACpC,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAA;wBACjC,SAAS,CAAC,eAAe,GAAG,GAAG,CAAA;wBAC/B,MAAK;oBACN,KAAK,yBAAyB,CAAC;oBAC/B,KAAK,8BAA8B;wBAClC,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;wBACpC,SAAS,CAAC,gBAAgB,GAAG,KAAK,CAAA;wBAClC,SAAS,CAAC,eAAe,GAAG,GAAG,CAAA;wBAC/B,MAAK;oBACN,KAAK,0BAA0B,CAAC;oBAChC,KAAK,+BAA+B;wBACnC,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;wBACpC,SAAS,CAAC,gBAAgB,GAAG,GAAG,CAAA;wBAChC,SAAS,CAAC,eAAe,GAAG,IAAI,CAAA;wBAChC,MAAK;oBACN,KAAK,wBAAwB;wBAC5B,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;wBACpC,0CAA0C;wBAC1C,SAAS,CAAC,UAAU,GAAG,CAAC,CAAA;wBACxB,SAAS,CAAC,gBAAgB,GAAG,IAAI,CAAA;wBACjC,SAAS,CAAC,eAAe,GAAG,KAAK,CAAA;wBACjC,MAAK;oBACN;wBACC,IAAI,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;4BACvC,SAAS,CAAC,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;4BAC1E,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;gCAC/B,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;gCACpC,SAAS,CAAC,gBAAgB,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAA;gCAC5E,8DAA8D;4BAC/D,CAAC;wBACF,CAAC;6BAAM,IAAI,QAAQ,CAAC,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE,CAAC;4BAC9C,SAAS,CAAC,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,gBAAgB,CAAC,CAAA;4BAC1E,IAAI,SAAS,CAAC,eAAe,EAAE,CAAC;gCAC/B,SAAS,CAAC,mBAAmB,GAAG,IAAI,CAAA;gCACpC,SAAS,CAAC,gBAAgB,GAAG,UAAU,CAAC,QAAQ,CAAC,OAAO,EAAE,iBAAiB,CAAC,CAAA;4BAC7E,CAAC;wBACF,CAAC;wBACD,MAAK;gBACP,CAAC;gBAED,MAAM,CAAC,QAAQ,CAAC,EAAE,CAAC,GAAG,SAAS,CAAA;YAChC,CAAC;QACF,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACtD,CAAC;QACD,MAAM,EAAE,CAAC,SAAS,CAAC,wBAAwB,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,CAAA;QACpE,OAAO,CAAC,GAAG,CAAC,qCAAqC,EAAE,MAAM,CAAC,CAAA;IAC3D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAA;QAEzD,0DAA0D;QAC1D,MAAM,YAAY,GAAG,MAAM,oBAAoB,CAAC,UAAU,CAAC,CAAA;QAC3D,IAAI,YAAY,EAAE,CAAC;YAClB,MAAM,GAAG,YAAY,CAAA;QACtB,CAAC;IACF,CAAC;IAED,kGAAkG;IAClG,0DAA0D;IAC1D,MAAM,WAAW,GAAwC,EAAE,CAAA;IAC3D,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;QACnD,WAAW,CAAC,GAAG,CAAC,GAAG;YAClB,SAAS,EAAE,KAAK,CAAC,SAAS,IAAI,CAAC;YAC/B,aAAa,EAAE,KAAK,CAAC,aAAa,IAAI,CAAC;YACvC,cAAc,EAAE,KAAK,CAAC,cAAc,IAAI,KAAK;YAC7C,mBAAmB,EAAE,KAAK,CAAC,mBAAmB,IAAI,KAAK;YACvD,UAAU,EAAE,KAAK,CAAC,UAAU,IAAI,CAAC;YACjC,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,CAAC;YACnC,gBAAgB,EAAE,KAAK,CAAC,gBAAgB,IAAI,CAAC;YAC7C,eAAe,EAAE,KAAK,CAAC,eAAe,IAAI,CAAC;YAC3C,WAAW,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE;SACpC,CAAA;IACF,CAAC;IAED,OAAO,6BAA6B,CAAC,MAAM,CAAC,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,CAAA;AACrE,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,oBAAoB,CAAC,UAAsB;IACzD,MAAM,wBAAwB,GAAG,IAAI,CAAC,IAAI,CAAC,MAAM,0BAA0B,CAAC,UAAU,CAAC,EAAE,eAAe,CAAC,gBAAgB,CAAC,CAAA;IAC1H,MAAM,UAAU,GAAG,MAAM,gBAAgB,CAAC,wBAAwB,CAAC,CAAA;IACnE,IAAI,UAAU,EAAE,CAAC;QAChB,IAAI,CAAC;YACJ,MAAM,YAAY,GAAG,MAAM,EAAE,CAAC,QAAQ,CAAC,wBAAwB,EAAE,MAAM,CAAC,CAAA;YACxE,OAAO,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,CAAA;QAChC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YAChB,OAAO,CAAC,KAAK,CAAC,yCAAyC,EAAE,KAAK,CAAC,CAAA;YAC/D,OAAO,SAAS,CAAA;QACjB,CAAC;IACF,CAAC;IACD,OAAO,SAAS,CAAA;AACjB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,0BAA0B,CAAC,UAAsB;IAC/D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,gBAAgB,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAC/E,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAA;IAC7C,OAAO,QAAQ,CAAA;AAChB,CAAC"}