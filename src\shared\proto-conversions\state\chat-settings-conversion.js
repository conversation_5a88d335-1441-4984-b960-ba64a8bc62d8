import { PlanActMode } from "../../../shared/proto/state";
/**
 * Converts domain ChatSettings objects to proto ChatSettings objects
 */
export function convertChatSettingsToProtoChatSettings(chatSettings) {
    return {
        mode: chatSettings.mode === "plan" ? PlanActMode.PLAN : PlanActMode.ACT,
        preferredLanguage: chatSettings.preferredLanguage,
        openAiReasoningEffort: chatSettings.openAIReasoningEffort,
    };
}
/**
 * Converts proto ChatSettings objects to domain ChatSettings objects
 */
export function convertProtoChatSettingsToChatSettings(protoChatSettings) {
    return {
        mode: protoChatSettings.mode === PlanActMode.PLAN ? "plan" : "act",
        preferredLanguage: protoChatSettings.preferredLanguage,
        openAIReasoningEffort: protoChatSettings.openAiReasoningEffort,
    };
}
/**
 * Converts domain ChatContent objects to proto ChatContent objects
 */
export function convertChatContentToProtoChatContent(chatContent) {
    if (!chatContent) {
        return undefined;
    }
    return {
        message: chatContent.message,
        images: chatContent.images || [],
    };
}
/**
 * Converts proto ChatContent objects to domain ChatContent objects
 */
export function convertProtoChatContentToChatContent(protoChatContent) {
    if (!protoChatContent) {
        return undefined;
    }
    return {
        message: protoChatContent.message,
        images: protoChatContent.images || [],
    };
}
//# sourceMappingURL=chat-settings-conversion.js.map