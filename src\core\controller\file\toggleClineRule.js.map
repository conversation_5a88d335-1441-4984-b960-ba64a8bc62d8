{"version": 3, "file": "toggleClineRule.js", "sourceRoot": "", "sources": ["toggleClineRule.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,cAAc,EAAE,iBAAiB,EAAE,iBAAiB,EAAE,oBAAoB,EAAE,MAAM,6BAA6B,CAAA;AAGxH;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,eAAe,CAAC,UAAsB,EAAE,OAA+B;IAC5F,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,CAAA;IAE/C,IAAI,CAAC,QAAQ,IAAI,OAAO,OAAO,KAAK,SAAS,IAAI,OAAO,QAAQ,KAAK,SAAS,EAAE,CAAC;QAChF,OAAO,CAAC,KAAK,CAAC,gDAAgD,EAAE;YAC/D,QAAQ;YACR,QAAQ,EAAE,OAAO,QAAQ,KAAK,SAAS,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,YAAY,OAAO,QAAQ,EAAE;YAClF,OAAO,EAAE,OAAO,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,YAAY,OAAO,OAAO,EAAE;SAC9E,CAAC,CAAA;QACF,MAAM,IAAI,KAAK,CAAC,mDAAmD,CAAC,CAAA;IACrE,CAAC;IAED,yDAAyD;IACzD,IAAI,QAAQ,EAAE,CAAC;QACd,MAAM,OAAO,GAAI,CAAC,MAAM,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAA0B,IAAI,EAAE,CAAA;QACrH,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAA;QAC3B,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,yBAAyB,EAAE,OAAO,CAAC,CAAA;IAChF,CAAC;SAAM,CAAC;QACP,MAAM,OAAO,GAAI,CAAC,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAA0B,IAAI,EAAE,CAAA;QACvH,OAAO,CAAC,QAAQ,CAAC,GAAG,OAAO,CAAA;QAC3B,MAAM,oBAAoB,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,EAAE,OAAO,CAAC,CAAA;IAClF,CAAC;IAED,kDAAkD;IAClD,MAAM,aAAa,GAAI,CAAC,MAAM,cAAc,CAAC,UAAU,CAAC,OAAO,EAAE,yBAAyB,CAAC,CAA0B,IAAI,EAAE,CAAA;IAC3H,MAAM,YAAY,GAAI,CAAC,MAAM,iBAAiB,CAAC,UAAU,CAAC,OAAO,EAAE,wBAAwB,CAAC,CAA0B,IAAI,EAAE,CAAA;IAE5H,OAAO;QACN,uBAAuB,EAAE,EAAE,OAAO,EAAE,aAAa,EAAE;QACnD,sBAAsB,EAAE,EAAE,OAAO,EAAE,YAAY,EAAE;KACjD,CAAA;AACF,CAAC"}