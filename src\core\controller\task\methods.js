// AUTO-GENERATED FILE - DO NOT MODIFY DIRECTLY
// Generated by proto/build-proto.js
// Import all method implementations
import { registerMethod } from "./index"
import { askResponse } from "./askResponse"
import { cancelTask } from "./cancelTask"
import { clearTask } from "./clearTask"
import { deleteNonFavoritedTasks } from "./deleteNonFavoritedTasks"
import { deleteTasksWithIds } from "./deleteTasksWithIds"
import { exportTaskWithId } from "./exportTaskWithId"
import { getTaskHistory } from "./getTaskHistory"
import { newTask } from "./newTask"
import { showTaskWithId } from "./showTaskWithId"
import { taskCompletionViewChanges } from "./taskCompletionViewChanges"
import { taskFeedback } from "./taskFeedback"
import { toggleTaskFavorite } from "./toggleTaskFavorite"
// Register all task service methods
export function registerAllMethods() {
	// Register each method with the registry
	registerMethod("askResponse", askResponse)
	registerMethod("cancelTask", cancelTask)
	registerMethod("clearTask", clearTask)
	registerMethod("deleteNonFavoritedTasks", deleteNonFavoritedTasks)
	registerMethod("deleteTasksWithIds", deleteTasksWithIds)
	registerMethod("exportTaskWithId", exportTaskWithId)
	registerMethod("getTaskHistory", getTaskHistory)
	registerMethod("newTask", newTask)
	registerMethod("showTaskWithId", showTaskWithId)
	registerMethod("taskCompletionViewChanges", taskCompletionViewChanges)
	registerMethod("taskFeedback", taskFeedback)
	registerMethod("toggleTaskFavorite", toggleTaskFavorite)
}
//# sourceMappingURL=methods.js.map
