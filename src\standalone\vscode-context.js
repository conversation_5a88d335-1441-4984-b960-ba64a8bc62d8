// @ts-nocheck
import * as vscode from "vscode";
import { log } from "./utils";
function stubUri(path) {
    console.log(`Using file path: ${path}`);
    return {
        fsPath: path,
        scheme: "",
        authority: "",
        path: "",
        query: "",
        fragment: "",
        with: function (change) {
            return stubUri(path);
        },
        toString: function (skipEncoding) {
            return path;
        },
        toJSON: function () {
            return {};
        },
    };
}
function createMemento() {
    const store = {};
    return {
        keys: function () {
            return Object.keys(store);
        },
        get: function (key) {
            return key in store ? store[key] : undefined;
        },
        update: function (key, value) {
            store[key] = value;
            return Promise.resolve();
        },
    };
}
const extensionContext = {
    extensionPath: "/tmp/vscode/extension",
    extensionUri: stubUri("/tmp/vscode/extension"),
    globalStoragePath: "/tmp/vscode/global",
    globalStorageUri: stubUri("/tmp/vscode/global"),
    storagePath: "/tmp/vscode/storage",
    storageUri: stubUri("/tmp/vscode/storage"),
    logPath: "/tmp/vscode/log",
    logUri: stubUri("/tmp/vscode/log"),
    globalState: createMemento(),
    workspaceState: createMemento(),
    storageState: createMemento(),
    environmentVariableCollection: {
        getScoped: function (scope) {
            return {
                persistent: false,
                description: undefined,
                replace: function (variable, value, options) { },
                append: function (variable, value, options) { },
                prepend: function (variable, value, options) { },
                get: function (variable) {
                    return undefined;
                },
                forEach: function (callback, thisArg) { },
                delete: function (variable) { },
                clear: function () { },
                [Symbol.iterator]: function () {
                    throw new Error("environmentVariableCollection.getScoped.Iterator not implemented");
                },
            };
        },
        persistent: false,
        description: undefined,
        replace: function (variable, value, options) { },
        append: function (variable, value, options) { },
        prepend: function (variable, value, options) { },
        get: function (variable) {
            return undefined;
        },
        forEach: function (callback, thisArg) {
            throw new Error("environmentVariableCollection.forEach not implemented");
        },
        delete: function (variable) { },
        clear: function () { },
        [Symbol.iterator]: function () {
            throw new Error("environmentVariableCollection.Iterator not implemented");
        },
    },
    extensionMode: 1, // Development
    extension: {
        id: "your.extension.id",
        isActive: true,
        extensionPath: "/tmp/vscode/extension",
        extensionUri: stubUri("/tmp/vscode/extension"),
        packageJSON: {},
        exports: {},
        activate: async () => { },
        extensionKind: vscode.ExtensionKind.UI,
    },
    subscriptions: [],
    asAbsolutePath: (relPath) => `/tmp/vscode/extension/${relPath}`,
    secrets: {
        store: async () => { },
        get: async () => undefined,
        delete: async () => { },
        onDidChange: {},
    },
};
const outputChannel = {
    append: (text) => process.stdout.write(text),
    appendLine: (line) => console.log(line),
    clear: () => { },
    show: () => { },
    hide: () => { },
    dispose: () => { },
    name: "",
    replace: function (value) { },
};
function postMessage(message) {
    log("postMessage called:", message);
    return Promise.resolve(true);
}
console.log("Finished loading vscode context...");
export { extensionContext, outputChannel, postMessage };
//# sourceMappingURL=vscode-context.js.map