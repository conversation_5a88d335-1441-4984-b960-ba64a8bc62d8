// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v2.7.0
//   protoc               v3.19.1
// source: web.proto

/* eslint-disable */
import { BinaryReader, BinaryWriter } from "@bufbuild/protobuf/wire"
import { StringRequest } from "./common"

export const protobufPackage = "cline"

export interface IsImageUrl {
	isImage: boolean
	url: string
}

export interface OpenGraphData {
	title: string
	description: string
	image: string
	url: string
	siteName: string
	type: string
}

function createBaseIsImageUrl(): IsImageUrl {
	return { isImage: false, url: "" }
}

export const IsImageUrl: MessageFns<IsImageUrl> = {
	encode(message: IsImageUrl, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.isImage !== false) {
			writer.uint32(8).bool(message.isImage)
		}
		if (message.url !== "") {
			writer.uint32(18).string(message.url)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): IsImageUrl {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseIsImageUrl()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 8) {
						break
					}

					message.isImage = reader.bool()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.url = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): IsImageUrl {
		return {
			isImage: isSet(object.isImage) ? globalThis.Boolean(object.isImage) : false,
			url: isSet(object.url) ? globalThis.String(object.url) : "",
		}
	},

	toJSON(message: IsImageUrl): unknown {
		const obj: any = {}
		if (message.isImage !== false) {
			obj.isImage = message.isImage
		}
		if (message.url !== "") {
			obj.url = message.url
		}
		return obj
	},

	create<I extends Exact<DeepPartial<IsImageUrl>, I>>(base?: I): IsImageUrl {
		return IsImageUrl.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<IsImageUrl>, I>>(object: I): IsImageUrl {
		const message = createBaseIsImageUrl()
		message.isImage = object.isImage ?? false
		message.url = object.url ?? ""
		return message
	},
}

function createBaseOpenGraphData(): OpenGraphData {
	return { title: "", description: "", image: "", url: "", siteName: "", type: "" }
}

export const OpenGraphData: MessageFns<OpenGraphData> = {
	encode(message: OpenGraphData, writer: BinaryWriter = new BinaryWriter()): BinaryWriter {
		if (message.title !== "") {
			writer.uint32(10).string(message.title)
		}
		if (message.description !== "") {
			writer.uint32(18).string(message.description)
		}
		if (message.image !== "") {
			writer.uint32(26).string(message.image)
		}
		if (message.url !== "") {
			writer.uint32(34).string(message.url)
		}
		if (message.siteName !== "") {
			writer.uint32(42).string(message.siteName)
		}
		if (message.type !== "") {
			writer.uint32(50).string(message.type)
		}
		return writer
	},

	decode(input: BinaryReader | Uint8Array, length?: number): OpenGraphData {
		const reader = input instanceof BinaryReader ? input : new BinaryReader(input)
		let end = length === undefined ? reader.len : reader.pos + length
		const message = createBaseOpenGraphData()
		while (reader.pos < end) {
			const tag = reader.uint32()
			switch (tag >>> 3) {
				case 1: {
					if (tag !== 10) {
						break
					}

					message.title = reader.string()
					continue
				}
				case 2: {
					if (tag !== 18) {
						break
					}

					message.description = reader.string()
					continue
				}
				case 3: {
					if (tag !== 26) {
						break
					}

					message.image = reader.string()
					continue
				}
				case 4: {
					if (tag !== 34) {
						break
					}

					message.url = reader.string()
					continue
				}
				case 5: {
					if (tag !== 42) {
						break
					}

					message.siteName = reader.string()
					continue
				}
				case 6: {
					if (tag !== 50) {
						break
					}

					message.type = reader.string()
					continue
				}
			}
			if ((tag & 7) === 4 || tag === 0) {
				break
			}
			reader.skip(tag & 7)
		}
		return message
	},

	fromJSON(object: any): OpenGraphData {
		return {
			title: isSet(object.title) ? globalThis.String(object.title) : "",
			description: isSet(object.description) ? globalThis.String(object.description) : "",
			image: isSet(object.image) ? globalThis.String(object.image) : "",
			url: isSet(object.url) ? globalThis.String(object.url) : "",
			siteName: isSet(object.siteName) ? globalThis.String(object.siteName) : "",
			type: isSet(object.type) ? globalThis.String(object.type) : "",
		}
	},

	toJSON(message: OpenGraphData): unknown {
		const obj: any = {}
		if (message.title !== "") {
			obj.title = message.title
		}
		if (message.description !== "") {
			obj.description = message.description
		}
		if (message.image !== "") {
			obj.image = message.image
		}
		if (message.url !== "") {
			obj.url = message.url
		}
		if (message.siteName !== "") {
			obj.siteName = message.siteName
		}
		if (message.type !== "") {
			obj.type = message.type
		}
		return obj
	},

	create<I extends Exact<DeepPartial<OpenGraphData>, I>>(base?: I): OpenGraphData {
		return OpenGraphData.fromPartial(base ?? ({} as any))
	},
	fromPartial<I extends Exact<DeepPartial<OpenGraphData>, I>>(object: I): OpenGraphData {
		const message = createBaseOpenGraphData()
		message.title = object.title ?? ""
		message.description = object.description ?? ""
		message.image = object.image ?? ""
		message.url = object.url ?? ""
		message.siteName = object.siteName ?? ""
		message.type = object.type ?? ""
		return message
	},
}

export type WebServiceDefinition = typeof WebServiceDefinition
export const WebServiceDefinition = {
	name: "WebService",
	fullName: "cline.WebService",
	methods: {
		checkIsImageUrl: {
			name: "checkIsImageUrl",
			requestType: StringRequest,
			requestStream: false,
			responseType: IsImageUrl,
			responseStream: false,
			options: {},
		},
		fetchOpenGraphData: {
			name: "fetchOpenGraphData",
			requestType: StringRequest,
			requestStream: false,
			responseType: OpenGraphData,
			responseStream: false,
			options: {},
		},
	},
} as const

type Builtin = Date | Function | Uint8Array | string | number | boolean | undefined

export type DeepPartial<T> = T extends Builtin
	? T
	: T extends globalThis.Array<infer U>
		? globalThis.Array<DeepPartial<U>>
		: T extends ReadonlyArray<infer U>
			? ReadonlyArray<DeepPartial<U>>
			: T extends {}
				? { [K in keyof T]?: DeepPartial<T[K]> }
				: Partial<T>

type KeysOfUnion<T> = T extends T ? keyof T : never
export type Exact<P, I extends P> = P extends Builtin
	? P
	: P & { [K in keyof P]: Exact<P[K], I[K]> } & { [K in Exclude<keyof I, KeysOfUnion<P>>]: never }

function isSet(value: any): boolean {
	return value !== null && value !== undefined
}

export interface MessageFns<T> {
	encode(message: T, writer?: BinaryWriter): BinaryWriter
	decode(input: BinaryReader | Uint8Array, length?: number): T
	fromJSON(object: any): T
	toJSON(message: T): unknown
	create<I extends Exact<DeepPartial<T>, I>>(base?: I): T
	fromPartial<I extends Exact<DeepPartial<T>, I>>(object: I): T
}
