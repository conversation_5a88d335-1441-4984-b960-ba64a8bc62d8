import { ContextManager } from "../ContextManager";
import { expect } from "chai";
describe("ContextManager", () => {
    function createMessages(count) {
        const messages = [];
        messages.push({
            role: "user",
            content: "Initial task message",
        });
        let role = "assistant";
        for (let i = 1; i < count; i++) {
            messages.push({
                role,
                content: `Message ${i}`,
            });
            role = role === "user" ? "assistant" : "user";
        }
        return messages;
    }
    describe("getNextTruncationRange", () => {
        let contextManager;
        beforeEach(() => {
            contextManager = new ContextManager();
        });
        it("first truncation with half keep", () => {
            const messages = createMessages(11);
            const result = contextManager.getNextTruncationRange(messages, undefined, "half");
            expect(result).to.deep.equal([2, 5]);
        });
        it("first truncation with quarter keep", () => {
            const messages = createMessages(11);
            const result = contextManager.getNextTruncationRange(messages, undefined, "quarter");
            expect(result).to.deep.equal([2, 7]);
        });
        it("sequential truncation with half keep", () => {
            const messages = createMessages(21);
            const firstRange = contextManager.getNextTruncationRange(messages, undefined, "half");
            expect(firstRange).to.deep.equal([2, 9]);
            // Pass the previous range for sequential truncation
            const secondRange = contextManager.getNextTruncationRange(messages, firstRange, "half");
            expect(secondRange).to.deep.equal([2, 13]);
        });
        it("sequential truncation with quarter keep", () => {
            const messages = createMessages(41);
            const firstRange = contextManager.getNextTruncationRange(messages, undefined, "quarter");
            const secondRange = contextManager.getNextTruncationRange(messages, firstRange, "quarter");
            expect(secondRange[0]).to.equal(2);
            expect(secondRange[1]).to.be.greaterThan(firstRange[1]);
        });
        it("ensures the last message in range is a user message", () => {
            const messages = createMessages(14);
            const result = contextManager.getNextTruncationRange(messages, undefined, "half");
            // Check if the message at the end of range is an assistant message
            const lastRemovedMessage = messages[result[1]];
            expect(lastRemovedMessage.role).to.equal("assistant");
            // Check if the next message after the range is a user message
            const nextMessage = messages[result[1] + 1];
            expect(nextMessage.role).to.equal("user");
        });
        it("handles small message arrays", () => {
            const messages = createMessages(3);
            const result = contextManager.getNextTruncationRange(messages, undefined, "half");
            expect(result).to.deep.equal([2, 1]);
        });
        it("preserves the message structure when truncating", () => {
            const messages = createMessages(20);
            const result = contextManager.getNextTruncationRange(messages, undefined, "half");
            // Get messages after removing the range
            const effectiveMessages = [...messages.slice(0, result[0]), ...messages.slice(result[1] + 1)];
            // Check first message and alternating pattern
            expect(effectiveMessages[0].role).to.equal("user");
            for (let i = 1; i < effectiveMessages.length; i++) {
                const expectedRole = i % 2 === 1 ? "assistant" : "user";
                expect(effectiveMessages[i].role).to.equal(expectedRole);
            }
        });
    });
    describe("getTruncatedMessages", () => {
        let contextManager;
        beforeEach(() => {
            contextManager = new ContextManager();
        });
        it("returns original messages when no range is provided", () => {
            const messages = createMessages(3);
            const result = contextManager.getTruncatedMessages(messages, undefined);
            expect(result).to.deep.equal(messages);
        });
        it("correctly removes messages in the specified range", () => {
            const messages = createMessages(5);
            const range = [1, 3];
            const result = contextManager.getTruncatedMessages(messages, range);
            expect(result).to.have.lengthOf(3);
            expect(result[0]).to.deep.equal(messages[0]);
            expect(result[1]).to.deep.equal(messages[1]);
            expect(result[2]).to.deep.equal(messages[4]);
        });
        it("works with a range that starts at the first message after task", () => {
            const messages = createMessages(4);
            const range = [1, 2];
            const result = contextManager.getTruncatedMessages(messages, range);
            expect(result).to.have.lengthOf(3);
            expect(result[0]).to.deep.equal(messages[0]);
            expect(result[1]).to.deep.equal(messages[1]);
            expect(result[2]).to.deep.equal(messages[3]);
        });
        it("correctly handles removing a range while preserving alternation pattern", () => {
            const messages = createMessages(5);
            const range = [2, 3];
            const result = contextManager.getTruncatedMessages(messages, range);
            expect(result).to.have.lengthOf(3);
            expect(result[0]).to.deep.equal(messages[0]);
            expect(result[1]).to.deep.equal(messages[1]);
            expect(result[2]).to.deep.equal(messages[4]);
            expect(result[0].role).to.equal("user");
            expect(result[1].role).to.equal("assistant");
            expect(result[2].role).to.equal("user");
        });
    });
});
//# sourceMappingURL=ContextManager.test.js.map