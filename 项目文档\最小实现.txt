
# Cline SSH终端扩展设计方案

## 一、设计目标

开发一个在Cline基础上的SSH终端扩展功能，实现自动化SSH登录、远程命令执行以及交互式命令处理，让大模型能够智能地操作本地和远程终端。

## 二、系统架构设计

### 1. 终端类型分类体系

- **终端类型**：本地终端(Local)和远程终端(Remote)
- **终端标识**：每个终端拥有唯一ID和上下文信息
- **终端生命周期**：创建→连接→交互→释放

### 2. 命令处理框架

- **命令分类**：标准命令和交互式命令
- **交互处理器**：预期响应匹配和自动填充
- **状态检测器**：命令完成状态和提示符检测

### 3. 元数据存储系统

- **终端信息**：类型、Shell类别、IP地址、当前状态
- **会话上下文**：提示符模式、设备类型、连接时间

## 三、数据模型设计

### 1. 终端信息模型

```typescript
export interface TerminalInfo {
  // 基础信息
  terminal: vscode.Terminal;
  id: number;
  busy: boolean;
  lastCommand: string;
  
  // 终端类型信息
  terminalType: 'local' | 'remote';    // 终端类型
  shellType?: 'powershell' | 'bash' | 'cmd' | 'zsh' | 'other';  // shell类型
  
  // 远程信息
  remoteAddress?: string;              // 远程IP/主机地址
  deviceType?: 'linux' | 'network' | 'other';  // 设备类型
  
  // 会话状态
  isLongRunningSession?: boolean;      // 是否为长期会话
  sessionType?: 'standard' | 'interactive' | 'long_running';  // 会话类型
  lastPrompt?: string;                 // 最近检测到的提示符
  
  // 内部状态跟踪
  pendingCwdChange?: string;
  cwdResolved?: {
    resolve: () => void;
    reject: (error: Error) => void;
  };
}
```

### 2. 交互式命令模型

```typescript
export interface InteractionStep {
  prompt: string;            // 期望的提示关键字
  response: string;          // 要发送的响应
  hidden?: boolean;          // 是否隐藏输入(如密码)
  optional?: boolean;        // 是否为可选交互
  timeout?: number;          // 此步骤的超时时间(ms)
}

export interface CommandData {
  command: string;           // 执行的命令
  type: 'standard' | 'interactive';  // 命令类型
  interactions?: InteractionStep[];  // 交互步骤
  targetTerminal?: 'new' | 'existing' | number;  // 目标终端
  checkSuccess?: boolean;    // 是否检查执行成功
}
```

## 四、核心功能设计

### 1. 终端类型识别与管理

```typescript
// 终端创建时自动识别本地shell类型
private detectLocalShellType(terminal: vscode.Terminal): string {
  // 基于平台和环境变量判断
  if (process.platform === 'win32') {
    return process.env.SHELL?.includes('powershell') ? 'powershell' : 'cmd';
  } else {
    return process.env.SHELL?.includes('zsh') ? 'zsh' : 'bash';
  }
}

// SSH命令成功后更新终端类型
private updateTerminalToRemote(terminalInfo: TerminalInfo, command: string) {
  // 提取主机信息
  const hostMatch = command.match(/ssh\s+(?:-\w+\s+)*([^@\s]+@)?([^@\s]+)/);
  if (hostMatch) {
    const host = hostMatch[2];
    terminalInfo.terminalType = 'remote';
    terminalInfo.remoteAddress = host;
    terminalInfo.isLongRunningSession = true;
    
    // 更新注册表
    TerminalRegistry.updateTerminal(terminalInfo.id, {
      terminalType: 'remote',
      remoteAddress: host,
      isLongRunningSession: true,
      sessionType: 'long_running'
    });
    
    console.log(`[DEBUG] 终端${terminalInfo.id}已转换为远程终端，远程主机: ${host}`);
  }
}
```

### 2. 智能交互式命令处理

```typescript
// 增强的凭证处理器
private setupInteractionHandler(
  terminalInfo: TerminalInfo, 
  interactions: InteractionStep[]
): vscode.Disposable {
  let currentStep = 0;
  
  const listener = terminalInfo.terminal.onDidWriteData((data: string) => {
    if (currentStep >= interactions.length) return;
    
    const step = interactions[currentStep];
    if (data.includes(step.prompt)) {
      console.log(`[DEBUG] 匹配到交互提示(${currentStep+1}/${interactions.length}): "${step.prompt}"`);
      
      // 发送响应并前进到下一步
      terminalInfo.terminal.sendText(step.response, !step.hidden);
      currentStep++;
      
      // 所有交互完成后清理
      if (currentStep >= interactions.length) {
        setTimeout(() => {
          this.cleanupInteractionHandler(terminalInfo.id);
        }, 1000); // 延迟清理以确保最后一个响应被处理
      }
    }
  });
  
  // 设置全局超时
  const timeoutId = setTimeout(() => {
    if (currentStep < interactions.length) {
      console.log(`[DEBUG] 交互超时，未完成所有步骤(${currentStep}/${interactions.length})`);
      this.cleanupInteractionHandler(terminalInfo.id);
    }
  }, 30000); // 30秒全局超时
  
  // 存储监听器和超时ID
  this.interactionListeners.set(terminalInfo.id, {
    listener,
    timeoutId,
    currentStep,
    totalSteps: interactions.length
  });
  
  return listener;
}
```

### 3. 命令执行状态检测

```typescript
// 错误模式库
private readonly errorPatterns = {
  local: {
    windows: [/不是内部或外部命令/, /无法识别/, /拒绝访问/, /错误代码/],
    unix: [/command not found/, /permission denied/, /No such file/, /Error/i]
  },
  remote: {
    linux: [/command not found/, /No such file/, /permission denied/, /Error:/],
    network: [/Invalid input/, /Incomplete command/, /Error:/, /% Invalid/]
  }
};

// 检测命令是否失败
private detectCommandFailure(output: string, terminalInfo: TerminalInfo): boolean {
  // 获取适用的错误模式集
  const isWindows = process.platform === 'win32';
  const patterns = terminalInfo.terminalType === 'local'
    ? this.errorPatterns.local[isWindows ? 'windows' : 'unix']
    : this.errorPatterns.remote[terminalInfo.deviceType || 'linux'];
  
  // 匹配错误模式
  return patterns.some(pattern => pattern.test(output));
}

// SSH连接状态检测
private detectSshSuccess(output: string): boolean {
  // SSH失败的常见错误
  const sshFailures = [
    /Permission denied/i,
    /Connection refused/i,
    /Could not resolve hostname/i,
    /No route to host/i,
    /Connection timed out/i,
    /Authentication failed/i
  ];
  
  // 检查是否包含错误信息
  const hasFailed = sshFailures.some(pattern => pattern.test(output));
  if (hasFailed) return false;
  
  // 检查是否有提示符(成功标志)
  return this.detectPromptInOutput(output);
}
```

## 五、状态转换逻辑

### 1. SSH会话状态转换

```typescript
// 检测SSH会话并处理状态转换
private checkForSshSession(command: string, terminalInfo: TerminalInfo) {
  // 判断是否为SSH命令
  if (command.trim().startsWith('ssh ') || command.includes('sshpass')) {
    console.log(`[DEBUG] 检测到SSH命令: ${command}`);
    // 设置会话类型为潜在远程会话
    terminalInfo.sessionType = 'long_running';
    
    // 设置SSH检测点
    setTimeout(() => {
      if (this.processes.has(terminalInfo.id)) {
        // 获取当前输出并分析
        const output = this.getProcessOutput(terminalInfo.id);
        
        // 检查SSH是否成功连接
        if (this.detectSshSuccess(output)) {
          // 连接成功，转换为远程终端
          this.updateTerminalToRemote(terminalInfo, command);
          
          // 设置提示符检测器以支持远程命令
          this.setupPromptDetector(terminalInfo);
          console.log(`[DEBUG] SSH连接成功，终端${terminalInfo.id}已设置为远程终端`);
        } else {
          console.log(`[DEBUG] SSH连接似乎未成功，终端${terminalInfo.id}保持为本地终端`);
          // 重置会话类型
          terminalInfo.sessionType = 'standard';
        }
      }
    }, 5000); // 5秒检查点
  }
}
```

### 2. 命令执行状态机

```typescript
// 命令状态类型
type CommandState = 'pending' | 'running' | 'interactive' | 'completed' | 'failed';

// 命令执行状态机
class CommandStateMachine {
  private state: CommandState = 'pending';
  private interactionCount: number = 0;
  private completedInteractions: number = 0;
  
  constructor(
    private readonly terminalInfo: TerminalInfo,
    private readonly command: string,
    private readonly interactions?: InteractionStep[]
  ) {
    this.interactionCount = interactions?.length || 0;
  }
  
  // 启动命令
  start() {
    this.state = 'running';
    if (this.interactionCount > 0) {
      this.state = 'interactive';
    }
  }
  
  // 处理交互步骤完成
  interactionCompleted() {
    this.completedInteractions++;
    if (this.completedInteractions >= this.interactionCount) {
      this.state = 'running'; // 交互完成后回到运行状态
    }
  }
  
  // 处理命令完成
  complete(success: boolean) {
    this.state = success ? 'completed' : 'failed';
  }
  
  // 获取当前状态
  getState(): CommandState {
    return this.state;
  }
  
  // 检查是否需要人工干预
  needsIntervention(): boolean {
    // 如果交互过程中断，可能需要人工干预
    return this.state === 'interactive' && 
           this.completedInteractions < this.interactionCount;
  }
}
```

## 六、错误处理与恢复机制

### 1. 交互式命令失败恢复

```typescript
// 交互式命令恢复策略
private handleInteractionFailure(
  terminalInfo: TerminalInfo, 
  command: string,
  interactions: InteractionStep[],
  failedStep: number
): void {
  console.log(`[DEBUG] 交互失败，步骤 ${failedStep+1}/${interactions.length}`);
  
  // 尝试重新发送当前步骤的响应
  if (failedStep < interactions.length) {
    const step = interactions[failedStep];
    if (!step.optional) {
      console.log(`[DEBUG] 尝试重新发送响应: "${step.response}"`);
      terminalInfo.terminal.sendText(step.response, !step.hidden);
    } else {
      console.log(`[DEBUG] 跳过可选步骤 ${failedStep+1}`);
      // 尝试继续下一步骤
    }
  }
  
  // 记录失败并通知大模型
  terminalInfo.lastInteractionFailure = {
    command,
    failedStep,
    stepPrompt: failedStep < interactions.length ? interactions[failedStep].prompt : undefined
  };
}
```

### 2. SSH连接失败处理

```typescript
// SSH连接失败处理
private handleSshFailure(terminalInfo: TerminalInfo, output: string): void {
  // 分析失败原因
  let failureReason = 'unknown';
  
  if (output.includes('Permission denied')) {
    failureReason = 'auth_failed';
  } else if (output.includes('Connection refused')) {
    failureReason = 'connection_refused';
  } else if (output.includes('Could not resolve hostname')) {
    failureReason = 'dns_failed';
  } else if (output.includes('Connection timed out')) {
    failureReason = 'timeout';
  }
  
  // 记录失败原因
  terminalInfo.lastConnectionFailure = {
    reason: failureReason,
    timestamp: Date.now(),
    output: output.substring(output.length - 500) // 保留最后500个字符
  };
  
  console.log(`[DEBUG] SSH连接失败，原因: ${failureReason}`);
  
  // 重置终端状态
  terminalInfo.sessionType = 'standard';
  terminalInfo.isLongRunningSession = false;
}
```

## 七、大模型集成

### 1. 终端信息传递

```typescript
// 获取终端信息用于传递给大模型
getTerminalsForModel(): Array<{
  id: number;
  name: string;
  type: string;
  remote?: string;
  lastCommand?: string;
}> {
  return Array.from(this.terminalIds)
    .map((id) => TerminalRegistry.getTerminal(id))
    .filter((t): t is TerminalInfo => t !== undefined)
    .map((t) => ({
      id: t.id,
      name: t.terminal.name,
      type: t.terminalType || 'local',
      remote: t.terminalType === 'remote' ? t.remoteAddress : undefined,
      lastCommand: t.lastCommand
    }));
}
```

### 2. 命令建议格式

```typescript
// 示例: 大模型返回的命令数据格式
{
  "command": "ssh user@*************",
  "type": "interactive",
  "interactions": [
    {
      "prompt": "password:",
      "response": "secretpassword",
      "hidden": true
    },
    {
      "prompt": "Are you sure you want to continue connecting",
      "response": "yes",
      "optional": true
    }
  ],
  "checkSuccess": true
}
```

## 八、实施路线图

### 阶段一：基础架构升级（1-2周）
1. 扩展TerminalInfo接口，添加终端类型和相关字段
2. 实现本地Shell类型自动检测
3. 完善终端管理的生命周期机制

### 阶段二：SSH连接增强（2-3周）
1. 实现SSH会话检测和状态转换逻辑
2. 开发提示符识别系统
3. 添加SSH成功/失败判断机制

### 阶段三：交互式命令支持（2-3周）
1. 实现交互步骤处理器
2. 开发凭证和预期响应匹配系统
3. 添加交互状态机和错误恢复逻辑

### 阶段四：大模型集成（1-2周）
1. 扩展提示模板支持新命令格式
2. 实现终端信息传递给大模型的接口
3. 开发命令执行结果的反馈机制

### 阶段五：测试与优化（2-3周）
1. 编写单元测试和集成测试
2. 进行各种网络环境下的测试
3. 优化性能和用户体验

## 九、注意事项与风险

1. **安全考虑**：明文密码处理需要谨慎，避免记录或意外泄露
2. **网络依赖**：SSH连接依赖网络状态，需要处理各种网络异常
3. **提示符多样性**：不同系统和设备的提示符格式差异很大，需要持续完善检测机制
4. **交互复杂性**：多轮交互可能陷入不可预期的状态，需要合理的超时和恢复机制
5. **设备兼容性**：不同网络设备的命令行界面差异较大，需要逐步扩展支持

通过这个设计方案，Cline将能够支持本地和远程终端的高效管理，处理复杂的交互式命令，并为大模型提供更丰富的终端操作能力，大幅提升在网络运维场景中的实用性。
