{"version": 3, "file": "TerminalExtension.test.js", "sourceRoot": "", "sources": ["TerminalExtension.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,QAAQ,EAAE,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,OAAO,CAAA;AAC3D,OAAO,QAAQ,CAAA;AACf,OAAO,KAAK,KAAK,MAAM,OAAO,CAAA;AAC9B,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAChC,OAAO,EAAE,eAAe,EAAE,MAAM,oBAAoB,CAAA;AACpD,OAAO,EAAE,gBAAgB,EAAE,MAAM,qBAAqB,CAAA;AAGtD,uBAAuB;AACvB,MAAM,YAAY;IACR,KAAK,CAAQ;IACb,SAAS,GAAY,KAAK,CAAA;IAC1B,wBAAwB,GAAkC,EAAE,CAAA;IAEpE,YAAY,IAAY;QACtB,IAAI,CAAC,KAAK,GAAG,IAAI,CAAA;IACnB,CAAC;IAED,IAAI,IAAI;QACN,OAAO,IAAI,CAAC,KAAK,CAAA;IACnB,CAAC;IAED,OAAO;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAA;IACvB,CAAC;IAED,IAAI,QAAQ;QACV,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,UAAoB;QACzC,OAAO,CAAC,GAAG,CAAC,gCAAgC,IAAI,iBAAiB,UAAU,EAAE,CAAC,CAAA;IAChF,CAAC;IAED,IAAI;QACF,OAAO,CAAC,GAAG,CAAC,oCAAoC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAA;IAC9D,CAAC;IAED,cAAc,CAAC,QAAgC;QAC7C,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAA;QAC5C,OAAO;YACL,OAAO,EAAE,GAAG,EAAE;gBACZ,MAAM,KAAK,GAAG,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAA;gBAC7D,IAAI,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC;oBACjB,IAAI,CAAC,wBAAwB,CAAC,MAAM,CAAC,KAAK,EAAE,CAAC,CAAC,CAAA;gBAChD,CAAC;YACH,CAAC;SACF,CAAA;IACH,CAAC;IAED,4CAA4C;IAC5C,cAAc,CAAC,IAAY;QACzB,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAA;IACnE,CAAC;CACF;AAED,QAAQ,CAAC,oBAAoB,EAAE,GAAG,EAAE;IAClC,IAAI,OAA2B,CAAA;IAC/B,IAAI,eAAgC,CAAA;IACpC,IAAI,YAA0B,CAAA;IAE9B,UAAU,CAAC,GAAG,EAAE;QACd,OAAO,GAAG,KAAK,CAAC,aAAa,EAAE,CAAA;QAE/B,oCAAoC;QACpC,YAAY,GAAG,IAAI,YAAY,CAAC,OAAO,CAAC,CAAA;QACxC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAC,OAAO,CAAC,YAAmB,CAAC,CAAA;QAE1E,6CAA6C;QAC7C,eAAe,GAAG,IAAI,eAAe,EAAE,CAAA;IACzC,CAAC,CAAC,CAAA;IAEF,SAAS,CAAC,GAAG,EAAE;QACb,OAAO,CAAC,OAAO,EAAE,CAAA;QACjB,4BAA4B;QAC5B,MAAM,SAAS,GAAG,gBAAgB,CAAC,eAAe,EAAE,CAAA;QACpD,SAAS,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,gBAAgB,CAAC,cAAc,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;IAC/D,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;QACtE,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA;QAE3E,uBAAuB;QACvB,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QACtE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAA;QAC9C,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,CAAC,CAAA;IAC1E,CAAC,CAAC,CAAA;IAEF,EAAE,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QAClD,MAAM,YAAY,GAAG,MAAM,eAAe,CAAC,mBAAmB,CAAC,WAAW,CAAC,CAAA;QAE3E,iDAAiD;QACjD,MAAM,WAAW,GAAgB;YAC/B,OAAO,EAAE,sBAAsB;YAC/B,IAAI,EAAE,aAAa;YACnB,YAAY,EAAE;gBACZ;oBACE,MAAM,EAAE,WAAW;oBACnB,QAAQ,EAAE,cAAc;oBACxB,MAAM,EAAE,IAAI;iBACb;gBACD;oBACE,MAAM,EAAE,8CAA8C;oBACtD,QAAQ,EAAE,KAAK;oBACf,QAAQ,EAAE,IAAI;iBACf;aACF;SACF,CAAA;QAED,8BAA8B;QAC9B,MAAM,OAAO,GAAG,eAAe,CAAC,qBAAqB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAA;QAEhF,2BAA2B;QAC3B,UAAU,CAAC,GAAG,EAAE;YACb,YAAoB,CAAC,cAAc,CAAC,8BAA8B,CAAC,CAAA;QACtE,CAAC,EAAE,GAAG,CAAC,CAAA;QAEP,iCAAiC;QACjC,UAAU,CAAC,GAAG,EAAE;YACb,YAAoB,CAAC,cAAc,CAAC,wDAAwD,CAAC,CAAA;QAChG,CAAC,EAAE,GAAG,CAAC,CAAA;QAEP,iCAAiC;QACjC,UAAU,CAAC,GAAG,EAAE;YACb,YAAoB,CAAC,cAAc,CAAC,2CAA2C,CAAC,CAAA;QACnF,CAAC,EAAE,GAAG,CAAC,CAAA;QAEP,+BAA+B;QAC/B,MAAM,OAAO,CAAA;QAEb,6CAA6C;QAC7C,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;QACvE,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,aAAa,CAAC,CAAA;QAC7E,YAAY,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,sBAAsB,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAC7E,CAAC,CAAC,CAAA;AACJ,CAAC,CAAC,CAAA"}