{"version": 3, "file": "ClineIgnoreController.test.js", "sourceRoot": "", "sources": ["ClineIgnoreController.test.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,qBAAqB,EAAE,MAAM,yBAAyB,CAAA;AAC/D,OAAO,EAAE,MAAM,aAAa,CAAA;AAC5B,OAAO,IAAI,MAAM,MAAM,CAAA;AACvB,OAAO,EAAE,MAAM,IAAI,CAAA;AACnB,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE,EAAE,MAAM,OAAO,CAAA;AACvD,OAAO,QAAQ,CAAA;AAEf,QAAQ,CAAC,uBAAuB,EAAE,GAAG,EAAE;IACtC,IAAI,OAAe,CAAA;IACnB,IAAI,UAAiC,CAAA;IAErC,UAAU,CAAC,KAAK,IAAI,EAAE;QACrB,sCAAsC;QACtC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CAAA;QACjG,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,CAAA;QAEvB,mCAAmC;QACnC,MAAM,EAAE,CAAC,SAAS,CACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAClC,CAAC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,qBAAqB,EAAE,EAAE,EAAE,QAAQ,EAAE,2BAA2B,EAAE,YAAY,CAAC,CAAC,IAAI,CACpH,IAAI,CACJ,CACD,CAAA;QAED,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;QAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;IAC9B,CAAC,CAAC,CAAA;IAEF,KAAK,CAAC,KAAK,IAAI,EAAE;QAChB,0BAA0B;QAC1B,MAAM,EAAE,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;IACvD,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,kBAAkB,EAAE,GAAG,EAAE;QACjC,kEAAkE;QAClE,qBAAqB;QACrB,uCAAuC;QACvC,8CAA8C;QAC9C,4DAA4D;QAC5D,KAAK;QACL,yDAAyD;QACzD,KAAK;QAEL,EAAE,CAAC,sCAAsC,EAAE,KAAK,IAAI,EAAE;YACrD,MAAM,OAAO,GAAG;gBACf,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC;gBACzC,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC;gBACtC,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC;aACzC,CAAA;YACD,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;YACxD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;QACzB,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,gDAAgD,EAAE,KAAK,IAAI,EAAE;YAC/D,MAAM,OAAO,GAAG;gBACf,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC;gBAC1C,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC;gBAC7C,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC;gBACtC,UAAU,CAAC,cAAc,CAAC,yBAAyB,CAAC;gBACpD,UAAU,CAAC,cAAc,CAAC,8BAA8B,CAAC;aACzD,CAAA;YACD,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC,CAAA;QACtD,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,0CAA0C,EAAE,KAAK,IAAI,EAAE;YACzD,MAAM,OAAO,GAAG;gBACf,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAC;gBAC5C,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC;gBACxC,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC;gBAC7C,UAAU,CAAC,cAAc,CAAC,sBAAsB,CAAC;gBACjD,UAAU,CAAC,cAAc,CAAC,sBAAsB,CAAC;aACjD,CAAA;YACD,OAAO,CAAC,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,EAAE,CAAC,SAAS,CACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAClC,CAAC,UAAU,EAAE,UAAU,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CACrE,CAAA;YAED,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,OAAO,GAAG;gBACf,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE,6BAA6B;gBACzE,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,EAAE,yCAAyC;gBACjF,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,oCAAoC;aAC7E,CAAA;YAED,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA,CAAC,gBAAgB;YAC7C,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA,CAAC,YAAY;YACxC,OAAO,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA,CAAC,aAAa;QAC3C,CAAC,CAAC,CAAA;QAEF,8CAA8C;QAE9C,sDAAsD;QACtD,uBAAuB;QACvB,wCAAwC;QACxC,MAAM;QACN,4CAA4C;QAC5C,2DAA2D;QAC3D,0DAA0D;QAC1D,4CAA4C;QAC5C,qDAAqD;QACrD,qCAAqC;QACrC,gDAAgD;QAChD,yEAAyE;QACzE,kBAAkB;QAClB,KAAK;QAEL,mDAAmD;QAEnD,qBAAqB;QACrB,sBAAsB;QACtB,8EAA8E;QAC9E,oFAAoF;QACpF,uGAAuG;QAEvG,uCAAuC;QACvC,0FAA0F;QAC1F,6EAA6E;QAC7E,mFAAmF;QACnF,yFAAyF;QAEzF,wBAAwB;QACxB,kFAAkF;QAClF,uGAAuG;QACvG,iGAAiG;QACjG,KAAK;QAEL,iDAAiD;QACjD,wDAAwD;QACxD,+DAA+D;QAC/D,iDAAiD;QACjD,iDAAiD;QACjD,uDAAuD;QACvD,qDAAqD;QACrD,mDAAmD;QACnD,yDAAyD;QACzD,0DAA0D;QAC1D,KAAK;QAEL,EAAE,CAAC,wCAAwC,EAAE,KAAK,IAAI,EAAE;YACvD,0CAA0C;YAC1C,MAAM,EAAE,CAAC,SAAS,CACjB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAClC,CAAC,gBAAgB,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAC/D,CAAA;YAED,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,aAAa,CAAC,CAAA;YACvD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;QACzB,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,eAAe,EAAE,GAAG,EAAE;QAC9B,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACvE,4CAA4C;YAC5C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,aAAa,CAAC,CAAA;YACrD,MAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;YAC5D,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;YAE9B,+DAA+D;YAC/D,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,eAAe,CAAC,CAAA;YACvD,MAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;YAC5D,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAE/B,qDAAqD;YACrD,MAAM,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,kBAAkB,CAAC,CAAA;YAC7D,MAAM,gBAAgB,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;YAClE,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;QACnC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,wDAAwD,EAAE,KAAK,IAAI,EAAE;YACvE,4CAA4C;YAC5C,MAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,eAAe,CAAC,CAAA;YAChE,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;YAE9B,+DAA+D;YAC/D,MAAM,aAAa,GAAG,UAAU,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAA;YAClE,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAE/B,qDAAqD;YACrD,MAAM,gBAAgB,GAAG,UAAU,CAAC,cAAc,CAAC,oBAAoB,CAAC,CAAA;YACxE,gBAAgB,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;QACnC,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,yCAAyC,EAAE,KAAK,IAAI,EAAE;YACxD,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAA;YACxD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QACxB,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;QAChC,EAAE,CAAC,iCAAiC,EAAE,KAAK,IAAI,EAAE;YAChD,MAAM,KAAK,GAAG,CAAC,cAAc,EAAE,MAAM,EAAE,cAAc,EAAE,aAAa,EAAE,gBAAgB,CAAC,CAAA;YAEvF,MAAM,QAAQ,GAAG,UAAU,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;YAC9C,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,cAAc,EAAE,cAAc,EAAE,gBAAgB,CAAC,CAAC,CAAA;QAC9E,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,gBAAgB,EAAE,GAAG,EAAE;QAC/B,EAAE,CAAC,6BAA6B,EAAE,KAAK,IAAI,EAAE;YAC5C,iDAAiD;YACjD,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,WAAW,CAAC,CAAA;YACrD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QACxB,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,+CAA+C,EAAE,KAAK,IAAI,EAAE;YAC9D,8DAA8D;YAC9D,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,EAAE,kBAAkB,IAAI,CAAC,GAAG,EAAE,EAAE,CAAC,CAAA;YACvE,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAA;YAExB,IAAI,CAAC;gBACJ,MAAM,UAAU,GAAG,IAAI,qBAAqB,CAAC,QAAQ,CAAC,CAAA;gBACtD,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;gBAC7B,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAA;gBACpD,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;YACxB,CAAC;oBAAS,CAAC;gBACV,MAAM,EAAE,CAAC,EAAE,CAAC,QAAQ,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CAAA;YACxD,CAAC;QACF,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,kCAAkC,EAAE,KAAK,IAAI,EAAE;YACjD,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,EAAE,CAAC,CAAA;YAE1D,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,MAAM,MAAM,GAAG,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAA;YAC5D,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QACxB,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;IAEF,QAAQ,CAAC,mBAAmB,EAAE,GAAG,EAAE;QAClC,EAAE,CAAC,4CAA4C,EAAE,KAAK,IAAI,EAAE;YAC3D,8DAA8D;YAC9D,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,YAAY,CAAC,EAAE,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAEpF,6FAA6F;YAC7F,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAExG,6DAA6D;YAC7D,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,4EAA4E;YAC5E,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YACzD,+EAA+E;YAC/E,UAAU,CAAC,cAAc,CAAC,cAAc,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YAC3D,uEAAuE;YACvE,UAAU,CAAC,cAAc,CAAC,YAAY,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YACzD,gCAAgC;YAChC,UAAU,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QACrD,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,qDAAqD,EAAE,KAAK,IAAI,EAAE;YACpE,+DAA+D;YAC/D,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,CAAC,2BAA2B,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAEhG,4BAA4B;YAC5B,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,kHAAkH;YAClH,UAAU,CAAC,cAAc,CAAC,kBAAkB,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QAC/D,CAAC,CAAC,CAAA;QAEF,EAAE,CAAC,+EAA+E,EAAE,KAAK,IAAI,EAAE;YAC9F,6FAA6F;YAC7F,MAAM,EAAE,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,cAAc,CAAC,EAAE,CAAC,2BAA2B,EAAE,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAA;YAEzG,UAAU,GAAG,IAAI,qBAAqB,CAAC,OAAO,CAAC,CAAA;YAC/C,MAAM,UAAU,CAAC,UAAU,EAAE,CAAA;YAE7B,8DAA8D;YAC9D,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;YACvD,oDAAoD;YACpD,UAAU,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;QACvD,CAAC,CAAC,CAAA;IACH,CAAC,CAAC,CAAA;AACH,CAAC,CAAC,CAAA"}