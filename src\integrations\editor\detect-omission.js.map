{"version": 3, "file": "detect-omission.js", "sourceRoot": "", "sources": ["detect-omission.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,MAAM,MAAM,QAAQ,CAAA;AAEhC;;;;;GAKG;AACH,SAAS,kBAAkB,CAAC,mBAA2B,EAAE,cAAsB;IAC9E,MAAM,aAAa,GAAG,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IACrD,MAAM,QAAQ,GAAG,cAAc,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA;IAC3C,MAAM,gBAAgB,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,CAAC,CAAA;IAElG,MAAM,eAAe,GAAG;QACvB,UAAU,EAAE,yCAAyC;QACrD,OAAO,EAAE,6CAA6C;QACtD,UAAU,EAAE,6BAA6B;QACzC,cAAc,EAAE,sBAAsB;QACtC,UAAU,EAAE,uBAAuB;KACnC,CAAA;IAED,KAAK,MAAM,IAAI,IAAI,QAAQ,EAAE,CAAC;QAC7B,IAAI,eAAe,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC;YAC3D,MAAM,KAAK,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;YAC7C,IAAI,gBAAgB,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC;gBACjE,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;oBACnC,OAAO,IAAI,CAAA;gBACZ,CAAC;YACF,CAAC;QACF,CAAC;IACF,CAAC;IAED,OAAO,KAAK,CAAA;AACb,CAAC;AAED;;;;GAIG;AACH,MAAM,UAAU,mBAAmB,CAAC,mBAA2B,EAAE,cAAsB;IACtF,IAAI,kBAAkB,CAAC,mBAAmB,EAAE,cAAc,CAAC,EAAE,CAAC;QAC7D,MAAM,CAAC,MAAM;aACX,kBAAkB,CAClB,4FAA4F,EAC5F,oCAAoC,CACpC;aACA,IAAI,CAAC,CAAC,SAAS,EAAE,EAAE;YACnB,IAAI,SAAS,KAAK,oCAAoC,EAAE,CAAC;gBACxD,MAAM,CAAC,GAAG,CAAC,YAAY,CACtB,MAAM,CAAC,GAAG,CAAC,KAAK,CACf,yHAAyH,CACzH,CACD,CAAA;YACF,CAAC;QACF,CAAC,CAAC,CAAA;IACJ,CAAC;AACF,CAAC"}