{"version": 3, "file": "updateMcpTimeout.js", "sourceRoot": "", "sources": ["updateMcpTimeout.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,kCAAkC,EAAE,MAAM,sDAAsD,CAAA;AAIzG;;;;;GAKG;AACH,MAAM,CAAC,KAAK,UAAU,gBAAgB,CAAC,UAAsB,EAAE,OAAgC;IAC9F,IAAI,CAAC;QACJ,IAAI,OAAO,CAAC,UAAU,IAAI,OAAO,OAAO,CAAC,UAAU,KAAK,QAAQ,IAAI,OAAO,OAAO,CAAC,OAAO,KAAK,QAAQ,EAAE,CAAC;YACzG,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,MAAM,EAAE,sBAAsB,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,OAAO,CAAC,CAAA;YACvG,OAAO,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC,CAAA;YACrC,MAAM,mBAAmB,GAAG,kCAAkC,CAAC,UAAU,CAAC,CAAA;YAC1E,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,mBAAmB,CAAC,CAAA;YACvD,OAAO,EAAE,UAAU,EAAE,mBAAmB,EAAE,CAAA;QAC3C,CAAC;aAAM,CAAC;YACP,OAAO,CAAC,KAAK,CAAC,sCAAsC,CAAC,CAAA;YACrD,MAAM,IAAI,KAAK,CAAC,sCAAsC,CAAC,CAAA;QACxD,CAAC;IACF,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAChB,OAAO,CAAC,KAAK,CAAC,uCAAuC,OAAO,CAAC,UAAU,GAAG,EAAE,KAAK,CAAC,CAAA;QAClF,MAAM,KAAK,CAAA;IACZ,CAAC;AACF,CAAC"}