import { describe, it, beforeEach, afterEach } from "mocha";
import "should";
import * as sinon from "sinon";
import * as vscode from "vscode";
import { TerminalManager } from "../TerminalManager";
import { TerminalRegistry } from "../TerminalRegistry";
// Mock vscode.Terminal
class MockTerminal {
    _name;
    _disposed = false;
    _onDidWriteDataListeners = [];
    constructor(name) {
        this._name = name;
    }
    get name() {
        return this._name;
    }
    dispose() {
        this._disposed = true;
    }
    get disposed() {
        return this._disposed;
    }
    sendText(text, addNewLine) {
        console.log(`[MockTerminal] Sending text: ${text}, addNewLine: ${addNewLine}`);
    }
    show() {
        console.log(`[MockTerminal] Showing terminal: ${this.name}`);
    }
    onDidWriteData(listener) {
        this._onDidWriteDataListeners.push(listener);
        return {
            dispose: () => {
                const index = this._onDidWriteDataListeners.indexOf(listener);
                if (index !== -1) {
                    this._onDidWriteDataListeners.splice(index, 1);
                }
            }
        };
    }
    // Helper method to simulate terminal output
    simulateOutput(data) {
        this._onDidWriteDataListeners.forEach(listener => listener(data));
    }
}
describe("Terminal Extension", () => {
    let sandbox;
    let terminalManager;
    let mockTerminal;
    beforeEach(() => {
        sandbox = sinon.createSandbox();
        // Mock vscode.window.createTerminal
        mockTerminal = new MockTerminal("Cline");
        sandbox.stub(vscode.window, "createTerminal").returns(mockTerminal);
        // Create a new TerminalManager for each test
        terminalManager = new TerminalManager();
    });
    afterEach(() => {
        sandbox.restore();
        // Clean up TerminalRegistry
        const terminals = TerminalRegistry.getAllTerminals();
        terminals.forEach(t => TerminalRegistry.removeTerminal(t.id));
    });
    it("should create a terminal with correct type information", async () => {
        const terminalInfo = await terminalManager.getOrCreateTerminal("test/path");
        // Verify terminal info
        terminalInfo.should.have.property("terminalType").which.equal("local");
        terminalInfo.should.have.property("shellType");
        terminalInfo.should.have.property("sessionType").which.equal("standard");
    });
    it("should handle interactive commands", async () => {
        const terminalInfo = await terminalManager.getOrCreateTerminal("test/path");
        // Create command data for an interactive command
        const commandData = {
            command: "ssh <EMAIL>",
            type: "interactive",
            interactions: [
                {
                    prompt: "password:",
                    response: "testpassword",
                    hidden: true
                },
                {
                    prompt: "Are you sure you want to continue connecting",
                    response: "yes",
                    optional: true
                }
            ]
        };
        // Run the interactive command
        const process = terminalManager.runInteractiveCommand(terminalInfo, commandData);
        // Simulate password prompt
        setTimeout(() => {
            mockTerminal.simulateOutput("<EMAIL>'s password:");
        }, 100);
        // Simulate "Are you sure" prompt
        setTimeout(() => {
            mockTerminal.simulateOutput("Are you sure you want to continue connecting (yes/no)?");
        }, 200);
        // Simulate successful connection
        setTimeout(() => {
            mockTerminal.simulateOutput("Welcome to Ubuntu 20.04\nuser@example:~$ ");
        }, 300);
        // Wait for process to complete
        await process;
        // Verify terminal was updated to remote type
        terminalInfo.should.have.property("terminalType").which.equal("remote");
        terminalInfo.should.have.property("remoteAddress").which.equal("example.com");
        terminalInfo.should.have.property("isLongRunningSession").which.equal(true);
    });
});
//# sourceMappingURL=TerminalExtension.test.js.map